
src/components/FlowVersionModal.tsx
Remove this unused import of 'Eye'.

es2015
unused
+
Code Smell
Minor
Not assigned
2min effort
13 days ago
Complete the task associated to this "TODO" comment.

cwe
+
Code Smell
Info
Not assigned
0min effort
13 days ago
Complete the task associated to this "TODO" comment.

cwe
+
Code Smell
Info
Not assigned
0min effort
13 days ago
src/components/HandlebarsAutocomplete.tsx
Do not use Array index in keys

jsx
performance
...
+
Code Smell
Major
Not assigned
5min effort
15 days ago
src/components/LanguageDropdown.tsx
This assertion is unnecessary since it does not change the type of the expression.

redundant
+
Code Smell
Minor
Not assigned
1min effort
1 month ago
src/components/MicroFrontendWrapper.tsx
Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.

es2020
nullish-coalescing
+
Code Smell
Minor
Not assigned
5min effort
24 days ago
src/components/Pagination.tsx
This assertion is unnecessary since it does not change the type of the expression.

redundant
+
Code Smell
Minor
Not assigned
1min effort
28 days ago
Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.

es2020
nullish-coalescing
+
Code Smell
Minor
Not assigned
5min effort
28 days ago
Remove this useless assignment to variable "paginationState".

cwe
unused
+
Code Smell
Major
Not assigned
15min effort
28 days ago
Complete the task associated to this "TODO" comment.

cwe
+
Code Smell
Info
Not assigned
0min effort
28 days ago
Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.

es2020
nullish-coalescing
+
Code Smell
Minor
Not assigned
5min effort
28 days ago
This assertion is unnecessary since it does not change the type of the expression.

redundant
+
Code Smell
Minor
Not assigned
1min effort
28 days ago
src/components/dropdownButton.tsx
Complete the task associated to this "TODO" comment.

cwe
+
Code Smell
Info
Not assigned
0min effort
2 months ago
Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.

es2020
nullish-coalescing
+
Code Smell
Minor
Not assigned
5min effort
2 months ago
src/components/dropdownWithDivider.tsx
Complete the task associated to this "TODO" comment.

cwe
+
Code Smell
Info
Not assigned
0min effort
25 days ago
Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.

es2020
nullish-coalescing
+
Code Smell
Minor
Not assigned
5min effort
25 days ago
src/components/file-upload.tsx
Remove this unused import of 'useEffect'.

es2015
unused
+
Code Smell
Minor
Not assigned
2min effort
1 month ago
This assertion is unnecessary since it does not change the type of the expression.

redundant
+
Code Smell
Minor
Not assigned
1min effort
1 month ago
Use the "RegExp.exec()" method instead.

regex
+
Code Smell
Minor
Not assigned
5min effort
1 month ago
Do not use Array index in keys

jsx
performance
...
+
Code Smell
Major
Not assigned
5min effort
1 month ago
src/components/time-picker.tsx
Refactor this function to reduce its Cognitive Complexity from 17 to the 15 allowed.

brain-overload
+
Code Smell
Critical
Not assigned
7min effort
1 month ago
Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.

es2020
nullish-coalescing
+
Code Smell
Minor
Not assigned
5min effort
1 month ago
Extract this nested ternary operation into an independent statement.

confusing
+
Code Smell
Major
Not assigned
5min effort
1 month ago
src/hooks/useDebounce.ts
Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.

es2020
nullish-coalescing
+
Code Smell
Minor
Not assigned
5min effort
15 days ago
src/hooks/useWebSocket.ts
Remove this unused import of 'useCallback'.

es2015
unused
+
Code Smell
Minor
Not assigned
2min effort
8 days ago
src/lib/constant.tsx
Do not use Array index in keys

jsx
performance
...
+
Code Smell
Major
Not assigned
5min effort
25 days ago
src/lib/utils/optimizedTags.ts
Refactor this function to reduce its Cognitive Complexity from 22 to the 15 allowed.

brain-overload
+
Code Smell
Critical
Not assigned
12min effort
28 days ago
Extract this nested ternary operation into an independent statement.

confusing
+
Code Smell
Major
Not assigned
5min effort
28 days ago
Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.

es2020
nullish-coalescing
+
Code Smell
Minor
Not assigned
5min effort
28 days ago
Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.

es2020
nullish-coalescing
+
Code Smell
Minor
Not assigned
5min effort
28 days ago
Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.

es2020
nullish-coalescing
+
Code Smell
Minor
Not assigned
5min effort
28 days ago
Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.

es2020
nullish-coalescing
+
Code Smell
Minor
Not assigned
5min effort
28 days ago
Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.

es2020
nullish-coalescing
+
Code Smell
Minor
Not assigned
5min effort
28 days ago
Refactor this function to reduce its Cognitive Complexity from 22 to the 15 allowed.

brain-overload
+
Code Smell
Critical
Not assigned
12min effort
24 days ago
Complete the task associated to this "TODO" comment.

cwe
+
Code Smell
Info
Not assigned
0min effort
24 days ago
Refactor this function to reduce its Cognitive Complexity from 20 to the 15 allowed.

brain-overload
+
Code Smell
Critical
Not assigned
10min effort
24 days ago
Complete the task associated to this "TODO" comment.

cwe
+
Code Smell
Info
Not assigned
0min effort
24 days ago
src/modules/AgentTransfer/MainContent.tsx
Remove this unused import of 'PaginationLoader'.

es2015
unused
+
Code Smell
Minor
Not assigned
2min effort
8 days ago
Remove this unused import of 'cn'.

es2015
unused
+
Code Smell
Minor
Not assigned
2min effort
8 days ago
'@/store/api/chatBotApi' import is duplicated.

es2015
+
Code Smell
Minor
Not assigned
1min effort
8 days ago
Remove this useless assignment to variable "setAccessToken".

cwe
unused
+
Code Smell
Major
Not assigned
15min effort
8 days ago
Complete the task associated to this "TODO" comment.

cwe
+
Code Smell
Info
Not assigned
0min effort
28 days ago
src/.../Channels/WebChannel/components/WidgetSettingsContent.tsx
Complete the task associated to this "TODO" comment.

cwe
+
Code Smell
Info
Not assigned
0min effort
18 days ago
Complete the task associated to this "TODO" comment.

cwe
+
Code Smell
Info
Not assigned
0min effort
18 days ago
src/modules/Channels/Whatsapp/config.ts
Remove this unused import of 'WABANumber'.

es2015
unused
+
Code Smell
Minor
Not assigned
2min effort
15 days ago
src/modules/Channels/Whatsapp/index.tsx
Remove this unused import of 'useMemo'.

es2015
unused
+
Code Smell
Minor
Not assigned
2min effort
8 days ago
src/modules/Channels/index.tsx
'@/assets/icons/WebMobileBot.svg' import is duplicated.

es2015
+
Code Smell
Minor
Not assigned
1min effort
8 days ago
Remove this useless assignment to variable "refetch".

cwe
unused
+
Code Smell
Major
Not assigned
15min effort
8 days ago
Remove this useless assignment to variable "fetchedChannels".

cwe
unused
+
Code Smell
Major
Not assigned
15min effort
8 days ago
Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.

es2020
nullish-coalescing
+
Code Smell
Minor
Not assigned
5min effort
8 days ago
Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.

es2020
nullish-coalescing
+
Code Smell
Minor
Not assigned
5min effort
8 days ago
Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.

es2020
nullish-coalescing
+
Code Smell
Minor
Not assigned
5min effort
8 days ago
Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.

es2020
nullish-coalescing
+
Code Smell
Minor
Not assigned
5min effort
8 days ago
Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.

es2020
nullish-coalescing
+
Code Smell
Minor
Not assigned
5min effort
15 days ago
src/modules/Preview/ChatMessages.tsx
Do not use Array index in keys

jsx
performance
...
+
Code Smell
Major
Not assigned
5min effort
24 days ago
src/modules/Preview/DynamicFormPrompt.tsx
Remove this unused import of 'useEffect'.

es2015
unused
+
Code Smell
Minor
Not assigned
2min effort
17 days ago
Remove this unused import of 'useMemo'.

es2015
unused
+
Code Smell
Minor
Not assigned
2min effort
17 days ago
Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.

es2020
nullish-coalescing
+
Code Smell
Minor
Not assigned
5min effort
24 days ago
Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.

es2020
nullish-coalescing
+
Code Smell
Minor
Not assigned
5min effort
24 days ago
src/modules/Preview/Renderers/FeedbackRenderer.tsx
Refactor this function to reduce its Cognitive Complexity from 42 to the 15 allowed.

brain-overload
+
Code Smell
Critical
Not assigned
32min effort
20 days ago
Do not define components during render. React will see a new component type on every render and destroy the entire subtree\u8217s DOM nodes and state. Instead, move this component definition out of the parent component \u8220FeedbackRenderer\u8221 and pass data as props. If you want to allow component creation in props, set allowAsProps option to true.

jsx
performance
...
+
Code Smell
Major
Not assigned
5min effort
25 days ago
Do not define components during render. React will see a new component type on every render and destroy the entire subtree\u8217s DOM nodes and state. Instead, move this component definition out of the parent component \u8220FeedbackRenderer\u8221 and pass data as props. If you want to allow component creation in props, set allowAsProps option to true.

jsx
performance
...
+
Code Smell
Major
Not assigned
5min effort
25 days ago
src/modules/Preview/Renderers/MessageRenderer.tsx
Complete the task associated to this "TODO" comment.

cwe
+
Code Smell
Info
Not assigned
0min effort
18 days ago
src/modules/Preview/debugger/AiAnalysisLogs.tsx
Do not use Array index in keys

jsx
performance
...
+
Code Smell
Major
Not assigned
5min effort
27 days ago
src/modules/Preview/debugger/Logs.tsx
Do not use Array index in keys

jsx
performance
...
+
Code Smell
Major
Not assigned
5min effort
27 days ago
src/modules/Preview/debugger/SessionData.tsx
Do not use Array index in keys

jsx
performance
...
+
Code Smell
Major
Not assigned
5min effort
27 days ago
src/modules/Preview/preview-modal.tsx
Remove this useless assignment to variable "isAgentConnected".

cwe
unused
+
Code Smell
Major
Not assigned
15min effort
8 days ago
Remove this useless assignment to variable "setIsAgentConnected".

cwe
unused
+
Code Smell
Major
Not assigned
15min effort
8 days ago
Prefer using an optional chain expression instead, as it's more concise and easier to read.

No tags
+
Code Smell
Major
Not assigned
5min effort
1 month ago
Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.

es2020
nullish-coalescing
+
Code Smell
Minor
Not assigned
5min effort
8 days ago
Empty block statement.

suspicious
+
Code Smell
Major
Not assigned
5min effort
8 days ago
src/modules/Preview/schema.ts
Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.

es2020
nullish-coalescing
+
Code Smell
Minor
Not assigned
5min effort
24 days ago
Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.

es2020
nullish-coalescing
+
Code Smell
Minor
Not assigned
5min effort
1 month ago
Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.

es2020
nullish-coalescing
+
Code Smell
Minor
Not assigned
5min effort
24 days ago
Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.

es2020
nullish-coalescing
+
Code Smell
Minor
Not assigned
5min effort
1 month ago
Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.

es2020
nullish-coalescing
+
Code Smell
Minor
Not assigned
5min effort
24 days ago
Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.

es2020
nullish-coalescing
+
Code Smell
Minor
Not assigned
5min effort
17 days ago
Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.

es2020
nullish-coalescing
+
Code Smell
Minor
Not assigned
5min effort
17 days ago
Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.

es2020
nullish-coalescing
+
Code Smell
Minor
Not assigned
5min effort
17 days ago
Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.

es2020
nullish-coalescing
+
Code Smell
Minor
Not assigned
5min effort
24 days ago
Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.

es2020
nullish-coalescing
+
Code Smell
Minor
Not assigned
5min effort
24 days ago
This assertion is unnecessary since it does not change the type of the expression.

redundant
+
Code Smell
Minor
Not assigned
1min effort
17 days ago
Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.

es2020
nullish-coalescing
+
Code Smell
Minor
Not assigned
5min effort
17 days ago
Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.

es2020
nullish-coalescing
+
Code Smell
Minor
Not assigned
5min effort
24 days ago
Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.

es2020
nullish-coalescing
+
Code Smell
Minor
Not assigned
5min effort
24 days ago
Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.

es2020
nullish-coalescing
+
Code Smell
Minor
Not assigned
5min effort
17 days ago
Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.

es2020
nullish-coalescing
+
Code Smell
Minor
Not assigned
5min effort
24 days ago
Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.

es2020
nullish-coalescing
+
Code Smell
Minor
Not assigned
5min effort
17 days ago
Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.

es2020
nullish-coalescing
+
Code Smell
Minor
Not assigned
5min effort
17 days ago
Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.

es2020
nullish-coalescing
+
Code Smell
Minor
Not assigned
5min effort
24 days ago
Remove this commented out code.

unused
+
Code Smell
Major
Not assigned
5min effort
17 days ago
Complete the task associated to this "TODO" comment.

cwe
+
Code Smell
Info
Not assigned
0min effort
17 days ago
Remove this commented out code.

unused
+
Code Smell
Major
Not assigned
5min effort
1 month ago
Remove this commented out code.

unused
+
Code Smell
Major
Not assigned
5min effort
1 month ago
Remove this commented out code.

unused
+
Code Smell
Major
Not assigned
5min effort
1 month ago
Remove this commented out code.

unused
+
Code Smell
Major
Not assigned
5min effort
1 month ago
Remove this commented out code.

unused
+
Code Smell
Major
Not assigned
5min effort
1 month ago
src/.../bot-settings/languageSettings/LanguageItem.tsx
Extract this nested ternary operation into an independent statement.

confusing
+
Code Smell
Major
Not assigned
5min effort
20 days ago
src/.../bot-settings/languageSettings/index.tsx
Remove this useless assignment to variable "isLoadingAllLanguages".

cwe
unused
+
Code Smell
Major
Not assigned
15min effort
20 days ago
Remove this useless assignment to variable "isLoadingBotLanguages".

cwe
unused
+
Code Smell
Major
Not assigned
15min effort
20 days ago
src/modules/editor/EditorWrapper.tsx
Complete the task associated to this "TODO" comment.

cwe
+
Code Smell
Info
Not assigned
0min effort
1 month ago
src/modules/editor/PluginForms/BaseModal.tsx
Complete the task associated to this "TODO" comment.

cwe
+
Code Smell
Info
Not assigned
0min effort
1 month ago
src/modules/editor/PluginForms/agentTransfer.tsx
Remove this useless assignment to variable "botId".

cwe
unused
+
Code Smell
Major
Not assigned
15min effort
1 month ago
src/modules/editor/PluginForms/choice.tsx
Remove this unused import of 'useCallback'.

es2015
unused
+
Code Smell
Minor
Not assigned
2min effort
22 days ago
Remove this unused import of 'useEffect'.

es2015
unused
+
Code Smell
Minor
Not assigned
2min effort
22 days ago
Remove this unused import of 'debounce'.

es2015
unused
+
Code Smell
Minor
Not assigned
2min effort
22 days ago
Remove this unused import of 'uniqueId'.

es2015
unused
+
Code Smell
Minor
Not assigned
2min effort
22 days ago
Remove this useless assignment to variable "getValues".

cwe
unused
+
Code Smell
Major
Not assigned
15min effort
22 days ago
Remove this useless assignment to variable "watch".

cwe
unused
+
Code Smell
Major
Not assigned
15min effort
22 days ago
src/.../editor/PluginForms/utils/schema.ts
Refactor this function to reduce its Cognitive Complexity from 19 to the 15 allowed.

brain-overload
+
Code Smell
Critical
Not assigned
9min effort
23 days ago
Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.

es2020
nullish-coalescing
+
Code Smell
Minor
Not assigned
5min effort
8 days ago
Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.

es2020
nullish-coalescing
+
Code Smell
Minor
Not assigned
5min effort
1 month ago
Refactor this function to reduce its Cognitive Complexity from 32 to the 15 allowed.

brain-overload
+
Code Smell
Critical
Not assigned
22min effort
24 days ago
Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.

es2020
nullish-coalescing
+
Code Smell
Minor
Not assigned
5min effort
1 month ago
Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.

es2020
nullish-coalescing
+
Code Smell
Minor
Not assigned
5min effort
1 month ago
Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.

es2020
nullish-coalescing
+
Code Smell
Minor
Not assigned
5min effort
1 month ago
Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.

es2020
nullish-coalescing
+
Code Smell
Minor
Not assigned
5min effort
1 month ago
Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.

es2020
nullish-coalescing
+
Code Smell
Minor
Not assigned
5min effort
1 month ago
Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.

es2020
nullish-coalescing
+
Code Smell
Minor
Not assigned
5min effort
1 month ago
Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.

es2020
nullish-coalescing
+
Code Smell
Minor
Not assigned
5min effort
1 month ago
Use concise character class syntax '\d' instead of '[0-9]'.

regex
+
Code Smell
Minor
Not assigned
5min effort
1 month ago
Use concise character class syntax '\d' instead of '[0-9]'.

regex
+
Code Smell
Minor
Not assigned
5min effort
1 month ago
Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.

es2020
nullish-coalescing
+
Code Smell
Minor
Not assigned
5min effort
1 month ago
Use concise character class syntax '\d' instead of '[0-9]'.

regex
+
Code Smell
Minor
Not assigned
5min effort
1 month ago
Use concise character class syntax '\d' instead of '[0-9]'.

regex
+
Code Smell
Minor
Not assigned
5min effort
1 month ago
Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.

es2020
nullish-coalescing
+
Code Smell
Minor
Not assigned
5min effort
1 month ago
Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.

es2020
nullish-coalescing
+
Code Smell
Minor
Not assigned
5min effort
8 days ago
Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.

es2020
nullish-coalescing
+
Code Smell
Minor
Not assigned
5min effort
8 days ago
src/.../editor/PluginForms/utils/useValidation.ts
Update this function so that its implementation is not identical to the one on line 4.

confusing
duplicate
...
+
Code Smell
Major
Not assigned
15min effort
1 month ago
src/modules/editor/hooks/useEditor/eventHandlers.ts
Remove this unused import of 'ui'.

es2015
unused
+
Code Smell
Minor
Not assigned
2min effort
2 months ago
This assertion is unnecessary since it does not change the type of the expression.

redundant
+
Code Smell
Minor
Not assigned
1min effort
2 months ago
src/modules/editor/hooks/useEditor/index.ts
Remove this commented out code.

unused
+
Code Smell
Major
Not assigned
5min effort
2 months ago
Remove this commented out code.

unused
+
Code Smell
Major
Not assigned
5min effort
2 months ago
This assertion is unnecessary since it does not change the type of the expression.

redundant
+
Code Smell
Minor
Not assigned
1min effort
2 months ago
Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.

es2020
nullish-coalescing
+
Code Smell
Minor
Not assigned
5min effort
2 months ago
Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.

es2020
nullish-coalescing
+
Code Smell
Minor
Not assigned
5min effort
2 months ago
Complete the task associated to this "TODO" comment.

cwe
+
Code Smell
Info
Not assigned
0min effort
2 months ago
Complete the task associated to this "TODO" comment.

cwe
+
Code Smell
Info
Not assigned
0min effort
2 months ago
Remove this commented out code.

unused
+
Code Smell
Major
Not assigned
5min effort
2 months ago
Remove this commented out code.

unused
+
Code Smell
Major
Not assigned
5min effort
2 months ago
This assertion is unnecessary since it does not change the type of the expression.

redundant
+
Code Smell
Minor
Not assigned
1min effort
2 months ago
This assertion is unnecessary since it does not change the type of the expression.

redundant
+
Code Smell
Minor
Not assigned
1min effort
2 months ago
This assertion is unnecessary since it does not change the type of the expression.

redundant
+
Code Smell
Minor
Not assigned
1min effort
2 months ago
This assertion is unnecessary since it does not change the type of the expression.

redundant
+
Code Smell
Minor
Not assigned
1min effort
2 months ago
This assertion is unnecessary since it does not change the type of the expression.

redundant
+
Code Smell
Minor
Not assigned
1min effort
2 months ago
This assertion is unnecessary since it does not change the type of the expression.

redundant
+
Code Smell
Minor
Not assigned
1min effort
2 months ago
This assertion is unnecessary since it does not change the type of the expression.

redundant
+
Code Smell
Minor
Not assigned
1min effort
2 months ago
src/modules/editor/hooks/useEditor/util.ts
This assertion is unnecessary since it does not change the type of the expression.

redundant
+
Code Smell
Minor
Not assigned
1min effort
2 months ago
Complete the task associated to this "TODO" comment.

cwe
+
Code Smell
Info
Not assigned
0min effort
2 months ago
src/modules/editor/hooks/useEditorNodeHandler.ts
This assertion is unnecessary since it does not change the type of the expression.

redundant
+
Code Smell
Minor
Not assigned
1min effort
3 months ago
This assertion is unnecessary since it does not change the type of the expression.

redundant
+
Code Smell
Minor
Not assigned
1min effort
3 months ago
This assertion is unnecessary since it does not change the type of the expression.

redundant
+
Code Smell
Minor
Not assigned
1min effort
3 months ago
Remove this useless assignment to variable "isValidGraph".

cwe
unused
+
Code Smell
Major
Not assigned
15min effort
3 months ago
Remove this commented out code.

unused
+
Code Smell
Major
Not assigned
5min effort
3 months ago
src/modules/editor/hooks/useStencil.ts
Remove this redundant jump.

clumsy
redundant
+
Code Smell
Minor
Not assigned
1min effort
1 month ago
Refactor this function to reduce its Cognitive Complexity from 16 to the 15 allowed.

brain-overload
+
Code Smell
Critical
Not assigned
6min effort
3 months ago
Prefer using nullish coalescing operator (`??`) instead of a ternary expression, as it is simpler to read.

es2020
nullish-coalescing
+
Code Smell
Minor
Not assigned
5min effort
3 months ago
src/.../editor/joint-components/stencil.ts
Remove this commented out code.

unused
+
Code Smell
Major
Not assigned
5min effort
3 months ago
src/modules/editor/neuratalk-editor.tsx
Remove this useless assignment to variable "isEditorLoading".

cwe
unused
+
Code Smell
Major
Not assigned
15min effort
3 months ago
Remove this useless assignment to variable "error".

cwe
unused
+
Code Smell
Major
Not assigned
15min effort
3 months ago
Remove this useless assignment to variable "fetchError".

cwe
unused
+
Code Smell
Major
Not assigned
15min effort
1 month ago
Remove this useless assignment to variable "isLoading".

cwe
unused
+
Code Smell
Major
Not assigned
15min effort
1 month ago
Complete the task associated to this "TODO" comment.

cwe
+
Code Smell
Info
Not assigned
0min effort
3 months ago
Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.

es2020
nullish-coalescing
+
Code Smell
Minor
Not assigned
5min effort
3 months ago
Remove this useless assignment to variable "updateCurrentDetails".

cwe
unused
+
Code Smell
Major
Not assigned
15min effort
2 months ago
Remove this useless assignment to variable "removeLastUndoState".

cwe
unused
+
Code Smell
Major
Not assigned
15min effort
2 months ago
Remove this useless assignment to variable "canUndo".

cwe
unused
+
Code Smell
Major
Not assigned
15min effort
2 months ago
Remove this useless assignment to variable "canRedo".

cwe
unused
+
Code Smell
Major
Not assigned
15min effort
2 months ago
Complete the task associated to this "TODO" comment.

cwe
+
Code Smell
Info
Not assigned
0min effort
13 days ago
Complete the task associated to this "TODO" comment.

cwe
+
Code Smell
Info
Not assigned
0min effort
3 months ago
This assertion is unnecessary since it does not change the type of the expression.

redundant
+
Code Smell
Minor
Not assigned
1min effort
3 months ago
This assertion is unnecessary since it does not change the type of the expression.

redundant
+
Code Smell
Minor
Not assigned
1min effort
3 months ago
This assertion is unnecessary since it does not change the type of the expression.

redundant
+
Code Smell
Minor
Not assigned
1min effort
3 months ago
Complete the task associated to this "TODO" comment.

cwe
+
Code Smell
Info
Not assigned
0min effort
2 months ago
Remove this commented out code.

unused
+
Code Smell
Major
Not assigned
5min effort
3 months ago
Remove this commented out code.

unused
+
Code Smell
Major
Not assigned
5min effort
3 months ago
src/modules/editor/utils/common.ts
'execCommand' is deprecated.

cwe
obsolete
+
Code Smell
Minor
Not assigned
15min effort
18 days ago
src/modules/editor/utils/config.ts
Remove this redundant type alias and replace its occurrences with "string".

No tags
+
Code Smell
Major
Not assigned
5min effort
3 months ago
src/modules/editor/utils/jointJsToLeap.ts
Refactor this function to reduce its Cognitive Complexity from 72 to the 15 allowed.

brain-overload
+
Code Smell
Critical
Not assigned
1h2min effort
3 months ago
Complete the task associated to this "TODO" comment.

cwe
+
Code Smell
Info
Not assigned
0min effort
24 days ago
src/modules/editor/utils/jsonConstant.ts
Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.

es2020
nullish-coalescing
+
Code Smell
Minor
Not assigned
5min effort
1 month ago
Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.

es2020
nullish-coalescing
+
Code Smell
Minor
Not assigned
5min effort
3 months ago
Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.

es2020
nullish-coalescing
+
Code Smell
Minor
Not assigned
5min effort
1 month ago
Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.

es2020
nullish-coalescing
+
Code Smell
Minor
Not assigned
5min effort
3 months ago
src/modules/editor/utils/leapToJointJs.ts
Refactor this function to reduce its Cognitive Complexity from 38 to the 15 allowed.

brain-overload
+
Code Smell
Critical
Not assigned
28min effort
1 month ago
Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.

es2020
nullish-coalescing
+
Code Smell
Minor
Not assigned
5min effort
24 days ago
Complete the task associated to this "TODO" comment.

cwe
+
Code Smell
Info
Not assigned
0min effort
24 days ago
Complete the task associated to this "TODO" comment.

cwe
+
Code Smell
Info
Not assigned
0min effort
24 days ago
Complete the task associated to this "TODO" comment.

cwe
+
Code Smell
Info
Not assigned
0min effort
24 days ago
Complete the task associated to this "TODO" comment.

cwe
+
Code Smell
Info
Not assigned
0min effort
24 days ago
src/modules/editor/utils/tools.js
Add or update the header of this file.

No tags
+
Code Smell
Blocker
Not assigned
5min effort
3 months ago
Remove the unused function parameter "fun" or rename it to "_fun" to make intention explicit.

unused
+
Code Smell
Major
Not assigned
5min effort
22 days ago
Unexpected var, use let or const instead.

bad-practice
es2015
+
Code Smell
Critical
Not assigned
5min effort
3 months ago
Define this declaration in a local scope or bind explicitly the property to the global object.

No tags
+
Code Smell
Major
Not assigned
5min effort
3 months ago
Remove the unused function parameter "evt" or rename it to "_evt" to make intention explicit.

unused
+
Code Smell
Major
Not assigned
5min effort
3 months ago
Expected method shorthand.

convention
es2015
+
Code Smell
Minor
Not assigned
1min effort
3 months ago
Remove this useless assignment to variable "parentId".

cwe
unused
+
Code Smell
Major
Not assigned
15min effort
22 days ago
Remove this useless assignment to variable "parentId".

cwe
unused
+
Code Smell
Major
Not assigned
15min effort
22 days ago
Remove parentheses around the parameter of this arrow function.

convention
es2015
+
Code Smell
Minor
Not assigned
2min effort
3 months ago
Remove this commented out code.

unused
+
Code Smell
Major
Not assigned
5min effort
22 days ago
Remove the unused function parameter "evt" or rename it to "_evt" to make intention explicit.

unused
+
Code Smell
Major
Not assigned
5min effort
3 months ago
Expected method shorthand.

convention
es2015
+
Code Smell
Minor
Not assigned
1min effort
3 months ago
Unexpected string concatenation.

clumsy
es2015
+
Code Smell
Minor
Not assigned
5min effort
3 months ago
Refactor this function to reduce its Cognitive Complexity from 17 to the 15 allowed.

brain-overload
+
Code Smell
Critical
Not assigned
7min effort
22 days ago
Remove the unused function parameter "setModalTypeDetails" or rename it to "_setModalTypeDetails" to make intention explicit.

unused
+
Code Smell
Major
Not assigned
5min effort
22 days ago
'removeElement' is already declared in the upper scope on line 22 column 5.

pitfall
suspicious
+
Code Smell
Major
Not assigned
5min effort
3 months ago
Group all shorthand properties at either the beginning or end of this object declaration.

convention
es2015
+
Code Smell
Minor
Not assigned
1min effort
3 months ago
Remove the unused function parameter "evt" or rename it to "_evt" to make intention explicit.

unused
+
Code Smell
Major
Not assigned
5min effort
3 months ago
Expected method shorthand.

convention
es2015
+
Code Smell
Minor
Not assigned
1min effort
3 months ago
Expected { after 'if' condition.

pitfall
+
Code Smell
Critical
Not assigned
2min effort
22 days ago
'childs' is already declared in the upper scope on line 129 column 46.

pitfall
suspicious
+
Code Smell
Major
Not assigned
5min effort
3 months ago
Remove this useless assignment to variable "parentId".

cwe
unused
+
Code Smell
Major
Not assigned
15min effort
22 days ago
Remove this useless assignment to variable "parentId".

cwe
unused
+
Code Smell
Major
Not assigned
15min effort
22 days ago
Remove parentheses around the parameter of this arrow function.

convention
es2015
+
Code Smell
Minor
Not assigned
2min effort
3 months ago
Remove this commented out code.

unused
+
Code Smell
Major
Not assigned
5min effort
22 days ago
Ternary operator used.

brain-overload
+
Code Smell
Major
Not assigned
5min effort
3 months ago
src/modules/editor/widget/Stencil/NodeItem.tsx
This assertion is unnecessary since it does not change the type of the expression.

redundant
+
Code Smell
Minor
Not assigned
1min effort
24 days ago
src/modules/editor/widget/Stencil/NodeList.tsx
Extract this nested ternary operation into an independent statement.

confusing
+
Code Smell
Major
Not assigned
5min effort
8 days ago
src/modules/editor/widget/flowsPanel.tsx
Remove this useless assignment to variable "error".

cwe
unused
+
Code Smell
Major
Not assigned
15min effort
17 days ago
Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.

es2020
nullish-coalescing
+
Code Smell
Minor
Not assigned
5min effort
17 days ago
Complete the task associated to this "TODO" comment.

cwe
+
Code Smell
Info
Not assigned
0min effort
17 days ago
Extract this nested ternary operation into an independent statement.

confusing
+
Code Smell
Major
Not assigned
5min effort
3 months ago
Refactor this function to reduce its Cognitive Complexity from 26 to the 15 allowed.

brain-overload
+
Code Smell
Critical
Not assigned
16min effort
17 days ago
src/.../rich-text-editor/components/MenuBar.tsx
Remove this unused import of 'useRef'.

es2015
unused
+
Code Smell
Minor
Not assigned
2min effort
1 month ago
Remove this unused import of 'RTEditorState'.

es2015
unused
+
Code Smell
Minor
Not assigned
2min effort
1 month ago
Complete the task associated to this "TODO" comment.

cwe
+
Code Smell
Info
Not assigned
0min effort
9 hours ago
Do not use Array index in keys

jsx
performance
...
+
Code Smell
Major
Not assigned
5min effort
1 month ago
Do not use Array index in keys

jsx
performance
...
+
Code Smell
Major
Not assigned
5min effort
1 month ago
src/.../rich-text-editor/components/RichTextEditor.tsx
Do not use Array index in keys

jsx
performance
...
+
Code Smell
Major
Not assigned
5min effort
1 month ago
src/.../rich-text-editor/plugins/autocomplete.plugin.ts
Refactor this function to not always return the same value.

No tags
+
Code Smell
Blocker
Not assigned
4min effort
15 days ago
src/modules/rich-text-editor/utils/commands.ts
Remove this unused import of 'Transaction'.

es2015
unused
+
Code Smell
Minor
Not assigned
2min effort
9 hours ago
Refactor this function to reduce its Cognitive Complexity from 17 to the 15 allowed.

brain-overload
+
Code Smell
Critical
Not assigned
7min effort
9 hours ago
Refactor this function to reduce its Cognitive Complexity from 17 to the 15 allowed.

brain-overload
+
Code Smell
Critical
Not assigned
7min effort
9 hours ago
Complete the task associated to this "TODO" comment.

cwe
+
Code Smell
Info
Not assigned
0min effort
9 hours ago
Remove this commented out code.

unused
+
Code Smell
Major
Not assigned
5min effort
9 hours ago
Remove this commented out code.

unused
+
Code Smell
Major
Not assigned
5min effort
9 hours ago
Remove this commented out code.

unused
+
Code Smell
Major
Not assigned
5min effort
9 hours ago
src/modules/train/Entities/AddEntityForm.tsx
Remove this unused import of 'z'.

es2015
unused
+
Code Smell
Minor
Not assigned
2min effort
1 month ago
src/modules/train/Entities/index.tsx
Remove this unused import of 'useEffect'.

es2015
unused
+
Code Smell
Minor
Not assigned
2min effort
28 days ago
src/modules/train/FAQs/FaqList.tsx
Complete the task associated to this "TODO" comment.

cwe
+
Code Smell
Info
Not assigned
0min effort
28 days ago
src/modules/train/FAQs/QuestionCard.tsx
Complete the task associated to this "TODO" comment.

cwe
+
Code Smell
Info
Not assigned
0min effort
1 month ago
src/modules/train/FAQs/index.tsx
Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.

es2020
nullish-coalescing
+
Code Smell
Minor
Not assigned
5min effort
28 days ago
src/modules/train/FAQs/useAddQuestionForm.tsx
Refactor this function to reduce its Cognitive Complexity from 19 to the 15 allowed.

brain-overload
+
Code Smell
Critical
Not assigned
9min effort
1 month ago
Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.

es2020
nullish-coalescing
+
Code Smell
Minor
Not assigned
5min effort
1 month ago
Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.

es2020
nullish-coalescing
+
Code Smell
Minor
Not assigned
5min effort
1 month ago
src/modules/train/IntentUtterances/AddUtteranceForm.tsx
Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.

es2020
nullish-coalescing
+
Code Smell
Minor
Not assigned
5min effort
1 month ago
Complete the task associated to this "TODO" comment.

cwe
+
Code Smell
Info
Not assigned
0min effort
1 month ago
src/.../EntityHighlight/EntitiesDropdown.tsx
Complete the task associated to this "TODO" comment.

cwe
+
Code Smell
Info
Not assigned
0min effort
12 days ago
src/.../EntityHighlight/EntityHighlightInput.tsx
Remove this useless assignment to variable "history".

cwe
unused
+
Code Smell
Major
Not assigned
15min effort
12 days ago
Remove this useless assignment to variable "isTyping".

cwe
unused
+
Code Smell
Major
Not assigned
15min effort
12 days ago
Remove this useless assignment to variable "isLoading".

cwe
unused
+
Code Smell
Major
Not assigned
15min effort
12 days ago
Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.

es2020
nullish-coalescing
+
Code Smell
Minor
Not assigned
5min effort
12 days ago
Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.

es2020
nullish-coalescing
+
Code Smell
Minor
Not assigned
5min effort
12 days ago
This assertion is unnecessary since it does not change the type of the expression.

redundant
+
Code Smell
Minor
Not assigned
1min effort
12 days ago
Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.

es2020
nullish-coalescing
+
Code Smell
Minor
Not assigned
5min effort
12 days ago
Remove this useless assignment to variable "displayText".

cwe
unused
+
Code Smell
Major
Not assigned
15min effort
12 days ago
Refactor this function to reduce its Cognitive Complexity from 63 to the 15 allowed.

brain-overload
+
Code Smell
Critical
Not assigned
53min effort
12 days ago
Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.

es2020
nullish-coalescing
+
Code Smell
Minor
Not assigned
5min effort
12 days ago
Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.

es2020
nullish-coalescing
+
Code Smell
Minor
Not assigned
5min effort
12 days ago
Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.

es2020
nullish-coalescing
+
Code Smell
Minor
Not assigned
5min effort
12 days ago
Complete the task associated to this "TODO" comment.

cwe
+
Code Smell
Info
Not assigned
0min effort
12 days ago
src/modules/train/IntentUtterances/index.tsx
Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.

es2020
nullish-coalescing
+
Code Smell
Minor
Not assigned
5min effort
27 days ago
src/modules/train/index.tsx
Remove this unused import of 'useState'.

es2015
unused
+
Code Smell
Minor
Not assigned
2min effort
15 days ago
src/modules/train/schema.ts
Prefer using an optional chain expression instead, as it's more concise and easier to read.

No tags
+
Code Smell
Major
Not assigned
5min effort
1 month ago
src/pages/Home/ChatbotCard.tsx
Remove this unused import of 'AlertCircle'.

es2015
unused
+
Code Smell
Minor
Not assigned
2min effort
3 months ago
src/pages/Home/Home.tsx
Remove this useless assignment to variable "t".

cwe
unused
+
Code Smell
Major
Not assigned
15min effort
17 days ago
Remove this useless assignment to variable "error".

cwe
unused
+
Code Smell
Major
Not assigned
15min effort
17 days ago
Remove this useless assignment to variable "botsResponse".

cwe
unused
+
Code Smell
Major
Not assigned
15min effort
17 days ago
Remove this useless assignment to variable "isFetching".

cwe
unused
+
Code Smell
Major
Not assigned
15min effort
17 days ago
src/pages/NeuratalkBuilder/TabBar.tsx
Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.

es2020
nullish-coalescing
+
Code Smell
Minor
Not assigned
5min effort
18 days ago
src/pages/NeuratalkBuilder/Tabs.tsx
Remove this unused import of 'RoutesName'.

es2015
unused
+
Code Smell
Minor
Not assigned
2min effort
15 days ago
Complete the task associated to this "TODO" comment.

cwe
+
Code Smell
Info
Not assigned
0min effort
15 days ago
Extract this nested ternary operation into an independent statement.

confusing
+
Code Smell
Major
Not assigned
5min effort
28 days ago
Complete the task associated to this "TODO" comment.

cwe
+
Code Smell
Info
Not assigned
0min effort
20 days ago
src/pages/NeuratalkBuilder/header/index.tsx
Complete the task associated to this "TODO" comment.

cwe
+
Code Smell
Info
Not assigned
0min effort
16 days ago
src/pages/NeuratalkBuilder/index.tsx
This assertion is unnecessary since it does not change the type of the expression.

redundant
+
Code Smell
Minor
Not assigned
1min effort
17 days ago
src/store/api/faqApi.ts
Complete the task associated to this "TODO" comment.

cwe
+
Code Smell
Info
Not assigned
0min effort
28 days ago
Refactor this function to reduce its Cognitive Complexity from 17 to the 15 allowed.

brain-overload
+
Code Smell
Critical
Not assigned
7min effort
28 days ago
src/store/api/flowApi.ts
Complete the task associated to this "TODO" comment.

cwe
+
Code Smell
Info
Not assigned
0min effort
17 days ago
Complete the task associated to this "TODO" comment.

cwe
+
Code Smell
Info
Not assigned
0min effort
17 days ago
src/store/api/intentApi.ts
Complete the task associated to this "TODO" comment.

cwe
+
Code Smell
Info
Not assigned
0min effort
28 days ago
Complete the task associated to this "TODO" comment.

cwe
+
Code Smell
Info
Not assigned
0min effort
28 days ago
src/store/api/languageApi.ts
Remove this unused import of 'CreateLanguageRequest'.

es2015
unused
+
Code Smell
Minor
Not assigned
2min effort
20 days ago
src/store/api/studioApi.ts
Extract this nested ternary operation into an independent statement.

confusing
+
Code Smell
Major
Not assigned
5min effort
2 months ago
src/store/apiSlice.ts
This assertion is unnecessary since it does not change the type of the expression.

redundant
+
Code Smell
Minor
Not assigned
1min effort
1 month ago
src/store/helper.ts
Remove this unused import of 'RootState'.

es2015
unused
+
Code Smell
Minor
Not assigned
2min effort
29 days ago
Complete the task associated to this "TODO" comment.

cwe
+
Code Smell
Info
Not assigned
0min effort
29 days ago
src/types/index.ts
"appStart" is overridden by string in this union type.

redundant
+
Code Smell
Minor
Not assigned
5min effort
2 months ago
"appEnd" is overridden by string in this union type.

redundant
+
Code Smell
Minor
Not assigned
5min effort
2 months ago
"whatsapp" is overridden by string in this union type.

redundant
+
Code Smell
Minor
Not assigned
5min effort
2 months ago
"voice" is overridden by string in this union type.

redundant
+
Code Smell
Minor
Not assigned
5min effort
2 months ago
"email" is overridden by string in this union type.

redundant
+
Code Smell
Minor
Not assigned
5min effort
2 months ago
"sms" is overridden by string in this union type.

redundant
+
Code Smell
Minor
Not assigned
5min effort
2 months ago
"webhook" is overridden by string in this union type.

redundant
+
Code Smell
Minor
Not assigned
5min effort
2 months ago
"http" is overridden by string in this union type.

redundant
+
Code Smell
Minor
Not assigned
5min effort
2 months ago
"choice" is overridden by string in this union type.

redundant
+
Code Smell
Minor
Not assigned
5min effort
2 months ago
"repeat" is overridden by string in this union type.

redundant
+
Code Smell
Minor
Not assigned
5min effort
2 months ago
"script" is overridden by string in this union type.

redundant
+
Code Smell
Minor
Not assigned
5min effort
2 months ago
"addcontact" is overridden by string in this union type.

redundant
+
Code Smell
Minor
Not assigned
5min effort
2 months ago
Interface has only a call signature, you should use a function type instead.

function
type
+
Code Smell
Minor
Not assigned
5min effort
1 month ago
src/types/ui.type.ts
Consider removing 'undefined' type or '?' specifier, one of them is redundant.

redundant
+
Code Smell
Major
Not assigned
1min effort
1 month ago
src/utils/getCurrentTimeStamp.ts
Complete the task associated to this "TODO" comment.

cwe
+
Code Smell
Info
Not assigned
0min effort
8 days ago