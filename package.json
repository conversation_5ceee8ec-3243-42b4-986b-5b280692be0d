{"name": "ng-chat<PERSON>-ui", "version": "0.1.0", "private": true, "scripts": {"lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,json,css,md}\"", "dev": "webpack serve --env NODE_ENV=development", "build": "tsc --noEmit && webpack --env NODE_ENV=production", "start": "npx serve -s dist -l 8080", "pack:rappid": "tar -czf rappid.tgz -C rappid .", "prepare": "husky", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "coverage:generate": "vitest run --coverage && cp coverage/lcov.info reports/lcov.info", "build:sdk": "tsc --noEmit && webpack --config webpack.sdk.config.js", "serve:sdk": "serve dist-chatbot-sdk -l 8082", "analyze:sdk-bundle": "ANALYZE_BUNDLE=true npm run build:sdk"}, "config": {"commitizen": {"path": "./node_modules/cz-customizable"}, "cz-customizable": {"config": "./.cz-config.cjs"}}, "dependencies": {"@codemirror/lang-javascript": "^6.2.4", "@hookform/resolvers": "^3.9.1", "@lottiefiles/react-lottie-player": "^3.6.0", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "1.1.1", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "^1.1.1", "@radix-ui/react-tooltip": "1.1.6", "@reduxjs/toolkit": "2.8.2", "@uiw/react-codemirror": "^4.23.12", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.4", "date-fns": "^4.1.0", "embla-carousel-react": "8.5.1", "emoji-picker-react": "^4.13.3", "google-libphonenumber": "^3.2.42", "i18next": "^25.2.1", "lodash": "^4.17.21", "lucide-react": "^0.513.0", "prosemirror-commands": "^1.7.1", "prosemirror-history": "^1.4.1", "prosemirror-keymap": "^1.2.3", "prosemirror-model": "^1.25.1", "prosemirror-schema-basic": "^1.2.4", "prosemirror-schema-list": "^1.5.1", "prosemirror-state": "^1.4.3", "prosemirror-view": "^1.40.0", "rappid": "file:rappid.tgz", "react": "^18.3.1", "react-day-picker": "^9.8.0", "react-dom": "^18.3.1", "react-dropzone": "^14.3.8", "react-hook-form": "^7.62.0", "react-i18next": "^15.5.2", "react-international-phone": "^4.6.0", "react-redux": "latest", "react-resizable-panels": "^2.1.7", "react-rnd": "^10.5.2", "react-router-dom": "^6.3.0", "react-select": "^5.10.2", "recharts": "2.15.0", "redux": "latest", "socket.io-client": "^4.8.1", "sonner": "^1.7.1", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.3.1", "uuid": "^11.1.0", "vaul": "^0.9.6", "webpack": "^5.99.9", "zod": "^3.25.76"}, "devDependencies": {"@babel/core": "^7.27.1", "@babel/preset-env": "^7.27.2", "@babel/preset-react": "^7.27.1", "@babel/preset-typescript": "^7.27.1", "@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@eslint/js": "^9.27.0", "@pmmmwh/react-refresh-webpack-plugin": "^0.6.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/google-libphonenumber": "^7.4.30", "@types/ladjs__country-language": "^1.0.0", "@types/lodash": "^4.17.20", "@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "@vitest/coverage-v8": "^3.2.1", "@vitest/ui": "^3.2.1", "autoprefixer": "^10.4.20", "babel-loader": "^10.0.0", "commitizen": "^4.3.1", "css-loader": "^7.1.2", "dotenv": "^16.5.0", "eslint": "^9.27.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.4.0", "eslint-plugin-react": "^7.37.5", "file-loader": "^6.2.0", "globals": "^16.2.0", "html-webpack-plugin": "^5.6.3", "husky": "^9.1.7", "jsdom": "^26.1.0", "mini-css-extract-plugin": "^2.9.2", "postcss": "^8", "postcss-loader": "^8.1.1", "postcss-prefix-selector": "^2.1.1", "prettier": "^3.5.3", "react-refresh": "^0.17.0", "serve": "^14.2.3", "style-loader": "^4.0.0", "tailwindcss": "^3.4.17", "typescript": "^5", "typescript-eslint": "^8.32.1", "vitest": "^3.2.1", "webpack-bundle-analyzer": "^4.10.2", "webpack-cli": "^6.0.1", "webpack-dev-server": "^5.2.1"}}