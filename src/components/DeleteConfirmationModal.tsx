import React, { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { useTranslation } from 'react-i18next';

import { DialogTrigger } from '@radix-ui/react-dialog';
import { LoaderCircle } from 'lucide-react';

interface CommonProps {
  onConfirm: () => void;
  title: string;
  description: string;
  isDeleting?: boolean;
}

interface ControlledDeleteConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  children?: React.ReactNode;
}

type UncontrolledDeleteConfirmationModalProps = Partial<ControlledDeleteConfirmationModalProps> & {
  children: React.ReactNode;
};

type DeleteConfirmationModalProps = CommonProps &
  (ControlledDeleteConfirmationModalProps | UncontrolledDeleteConfirmationModalProps);

const DeleteConfirmationModal: React.FC<DeleteConfirmationModalProps> = props => {
  const {
    onConfirm,
    title,
    description,
    children,
    isOpen: externalIsOpen,
    onClose: externalOnClose,
    isDeleting,
  } = props;
  const { t } = useTranslation();
  const [internalIsOpen, setInternalIsOpen] = useState(false);

  const isControlled = externalIsOpen !== undefined;
  const isOpen = isControlled ? externalIsOpen : internalIsOpen;
  const onClose = isControlled ? externalOnClose : () => setInternalIsOpen(false);

  const handleOpenChange = (open: boolean) => {
    if (isControlled) {
      onClose?.();
    } else {
      setInternalIsOpen(open);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      {children && <DialogTrigger asChild>{children}</DialogTrigger>}
      <DialogContent className="sm:max-w-96" aria-modal="true" onClick={e => e.stopPropagation()}>
        <DialogHeader>
          <DialogTitle className="text-sm font-medium">{title}</DialogTitle>
        </DialogHeader>
        <div className="flex items-start py-4">
          <DialogDescription className="text-sm text-tertiary-600 leading-normal">
            {description}
          </DialogDescription>
        </div>
        <DialogFooter className="flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2">
          <Button type="button" variant="outline" variantColor="tertiary" onClick={onClose}>
            {t('chatbot.noCancel')}
          </Button>
          <Button type="button" onClick={onConfirm}>
            {isDeleting && <LoaderCircle className="w-4 h-4 animate-spin" />}

            {t('chatbot.yesDelete')}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default DeleteConfirmationModal;
