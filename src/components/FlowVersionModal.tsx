import React, { useEffect, useState } from 'react';
import { format } from 'date-fns';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useTranslation } from 'react-i18next';
import { useGetFlowVersionsQuery, useGetFlowVersionDataQuery } from '@/store/api/chatBotApi';
import { FlowVersion } from '@/types';
import { MoreVertical } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import NeuraTalkEditor from '@/modules/editor/neuratalk-editor';

interface FlowVersionModalProps {
  isOpen: boolean;
  onClose: () => void;
  appId: string;
}

const FlowVersionModal: React.FC<FlowVersionModalProps> = ({ isOpen, onClose, appId }) => {
  const { t } = useTranslation();
  const { data, isLoading, error } = useGetFlowVersionsQuery({ appId }, { skip: !appId });

  const [selectedVersion, setSelectedVersion] = useState<FlowVersion | null>(null);
  const {
    data: selectedVersionBotData,
    isLoading: isLoadingSelectedVersionData,
    error: selectedVersionError,
  } = useGetFlowVersionDataQuery(
    { appId, version: selectedVersion?.version || '' },
    { skip: !selectedVersion?.version }
  );

  const flowVersions = data?.data || [];

  const handlePreviewClick = (version: FlowVersion) => {
    setSelectedVersion(version);
  };

  useEffect(() => {
    if (flowVersions.length > 0 && !selectedVersion) {
      setSelectedVersion(flowVersions[0]);
    }
  }, [flowVersions]);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[1200px] h-[90vh] flex flex-col">
        <DialogHeader>
          <DialogTitle className="text-lg font-semibold">{t('versionControl.title')}</DialogTitle>
          <DialogDescription>{t('versionControl.description')}</DialogDescription>
        </DialogHeader>
        <div className="flex-1 flex overflow-hidden py-4">
          {/* Left Pane: Preview */}
          <div className="flex-1 w-0 border rounded-md p-4 flex flex-col items-center justify-center bg-tertiary-50">
            {isLoadingSelectedVersionData && <p>{t('common.loading')}</p>}
            {selectedVersionError && (
              <p className="text-error-500">{t('common.errorLoadingData')}</p>
            )}
            {!selectedVersion && !isLoadingSelectedVersionData && (
              <p className="text-tertiary-500">{t('versionControl.previewPlaceholder')}</p>
            )}
            {selectedVersionBotData?.data && !isLoadingSelectedVersionData && (
              <div className="w-full">
                <NeuraTalkEditor
                  id={appId}
                  jsonDetails={selectedVersionBotData.data}
                  readOnly={true}
                />
              </div>
            )}
          </div>

          {/* Right Pane: Version History */}
          <div className="w-1/4 pl-4 flex flex-col">
            <h3 className="text-md font-semibold mb-4">
              {t('versionControl.historyTitle', { count: flowVersions.length })}
            </h3>
            <ScrollArea className="flex-1 pr-2">
              {isLoading && <p>{t('common.loading')}</p>}
              {error && <p className="text-error-500">{t('common.errorLoadingData')}</p>}
              {!isLoading && !error && flowVersions.length === 0 && (
                <p className="text-tertiary-500">{t('versionControl.noVersions')}</p>
              )}
              <div className="space-y-3">
                {flowVersions.map((version: FlowVersion) => (
                  <div
                    key={version.version}
                    className="border rounded-md p-3 shadow-sm flex items-center justify-between cursor-pointer"
                    onClick={() => handlePreviewClick(version)}
                  >
                    <div>
                      <p className="font-medium">
                        {t('versionControl.version', { version: version.version })}
                      </p>
                      <p className="text-sm text-tertiary-600">
                        {version.comment} - {version.type}
                      </p>
                      <p className="text-xs text-tertiary-500">
                        {format(new Date(version.createdAt), 'dd MMM yyyy HH:mm')}
                      </p>
                    </div>
                    <div className="flex items-center gap-2">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="text-tertiary-500 hover:bg-tertiary-50"
                          >
                            <MoreVertical className="w-4 h-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem
                            onClick={() => {
                              console.log('Load version functionality to be implemented');
                            }}
                          >
                            {t('versionControl.loadVersion')}
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => {
                              console.log('Revert to version functionality to be implemented');
                            }}
                          >
                            {t('versionControl.revertToVersion')}
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default FlowVersionModal;
