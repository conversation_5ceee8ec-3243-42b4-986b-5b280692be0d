import React, { useState, useEffect, useRef } from 'react';
import { flattenApiData } from '@/lib/utils/handlebars.utils';

interface HandlebarsAutocompleteProps {
  value: string; // The full text content from the RichTextEditor
  onSelect: (selectedVariable: string) => void;
  onClose: () => void;
}

interface ApiNode {
  name: string;
  type: string;
  description: string;
  children?: ApiNode[];
  valueSchema?: ApiNode[];
}

const API_URL =
  'http://localhost:3000/api/v1/apps/cf6dac25-81e8-4e06-918d-14b415fa59f3/autocomplete/globalContext';

const HandlebarsAutocomplete: React.FC<HandlebarsAutocompleteProps> = ({
  value,
  onSelect,
  onClose,
}) => {
  const [apiData, setApiData] = useState<ApiNode[]>([]);
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const autocompleteRef = useRef<HTMLDivElement>(null);

  // Fetch API data once
  useEffect(() => {
    const fetchApiData = async () => {
      try {
        // In a real application, you'd use a proper fetch or axios call
        // For now, I'll simulate the fetch with the provided example data
        const response: ApiNode[] = [
          {
            name: 'globalContext',
            type: 'object',
            description: 'Global context object containing session data and user input history.',
            children: [
              {
                name: 'sessionData',
                type: 'object',
                description: 'Data stored in the current session.',
                children: [
                  {
                    name: 'httpResponse_4a6e490',
                    type: 'object',
                    description: 'Response from HTTP module: 4a6e490',
                    valueSchema: [
                      {
                        name: 'code',
                        type: 'number',
                        description: 'HTTP status code.',
                      },
                      {
                        name: 'msg',
                        type: 'any',
                        description: 'Response message or data.',
                      },
                    ],
                  },
                  {
                    name: 'b6007c2',
                    type: 'object',
                    description: 'Data from form module: b6007c2',
                    valueSchema: [
                      {
                        name: 'name',
                        type: 'text',
                        description: 'Form field: Provide your details (text)',
                      },
                      {
                        name: 'number',
                        type: 'mobile_number',
                        description: 'Form field:  (mobile_number)',
                      },
                    ],
                  },
                  {
                    name: 'e230fb1',
                    type: 'object',
                    description: 'Feedback data from module: e230fb1',
                    valueSchema: [
                      {
                        name: 'feedback',
                        type: 'any',
                        description: 'The feedback value provided by the user.',
                      },
                    ],
                  },
                ],
              },
              {
                name: 'userInputHistory',
                type: 'object',
                description: 'History of user inputs.',
                children: [
                  {
                    name: 'dynamic_input_key',
                    type: 'object',
                    description: 'A specific user input entry.',
                    children: [
                      {
                        name: 'value',
                        type: 'any',
                        description: 'The value of the user input.',
                      },
                      {
                        name: 'timestamp',
                        type: 'string',
                        description: 'Timestamp of the user input.',
                      },
                    ],
                  },
                ],
              },
            ],
          },
        ];
        setApiData(response);
      } catch (error) {
        console.error('Failed to fetch autocomplete data:', error);
      }
    };
    fetchApiData();
  }, []);

  // Effect to detect trigger and update search term
  useEffect(() => {
    const trigger = '{{';
    const lastIndex = value.lastIndexOf(trigger);

    if (lastIndex !== -1) {
      const textAfterTrigger = value.substring(lastIndex + trigger.length);
      const endIndex = textAfterTrigger.indexOf('}}');
      const currentInput =
        endIndex === -1 ? textAfterTrigger : textAfterTrigger.substring(0, endIndex);
      setSearchTerm(currentInput.trim());
    } else {
      setSearchTerm('');
      onClose(); // Close if trigger is not found
    }
  }, [value, onClose]);

  // Filter suggestions based on searchTerm and apiData
  useEffect(() => {
    if (searchTerm && apiData.length > 0) {
      const allFlattened = flattenApiData(apiData);
      const filtered = allFlattened.filter(item =>
        item.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setSuggestions(filtered);
    } else {
      setSuggestions([]);
    }
  }, [searchTerm, apiData]);

  const handleSelect = (suggestion: string) => {
    onSelect(suggestion);
    onClose();
  };

  if (!suggestions.length) {
    return null;
  }

  return (
    <div
      ref={autocompleteRef}
      className="absolute z-10 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto"
      style={{ top: '100%', left: 0, width: '100%' }} // Position relative to parent
    >
      {suggestions.map((suggestion) => (
        <div
          key={suggestion}
          className="px-4 py-2 cursor-pointer hover:bg-gray-100"
          onClick={() => handleSelect(suggestion)}
        >
          {suggestion}
        </div>
      ))}
    </div>
  );
};

export default HandlebarsAutocomplete;
