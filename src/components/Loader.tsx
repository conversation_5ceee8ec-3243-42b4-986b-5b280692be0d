import { useTranslation } from 'react-i18next';
import { cn } from '@/lib/utils';

export const Loader  = () => {
  const { t } = useTranslation();
    return (
        <div className={cn('flex justify-center items-center py-8 w-full h-full')}>
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"></div>
            <span className="ml-2 text-tertiary-600">{t('common.loading')}</span>
          </div>
    )
}