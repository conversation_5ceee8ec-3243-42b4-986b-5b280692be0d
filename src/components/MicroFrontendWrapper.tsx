import React, { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import '../styles/globals.css';
import '@/lib/i18n/config';
import { Toaster } from './ui/toaster';
import TailwindWrapper from './TailwindWrapper';
import { Provider } from 'react-redux';
import { store } from '@/store/store';

interface MicroFrontendWrapperProps {
  children: React.ReactNode;
  className?: string;
  fullscreen?: boolean;
}

const MicroFrontendWrapper: React.FC<MicroFrontendWrapperProps> = ({
  children,
  className,
  fullscreen = true,
}) => {
  const { i18n } = useTranslation();

  useEffect(() => {
    const languageCode = localStorage.getItem('i18nextLng') ?? 'en';
    if (i18n.language !== languageCode) {
      i18n.changeLanguage(languageCode);
    }
  }, [i18n]);

  return (
    <Provider store={store}>
      <TailwindWrapper className={className} fullscreen={fullscreen}>
        {children}
        <Toaster />
      </TailwindWrapper>
    </Provider>
  );
};

export default MicroFrontendWrapper;
