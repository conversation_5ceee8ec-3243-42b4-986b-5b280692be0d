import React, {
  createContext,
  useContext,
  useState,
  useCallback,
  useEffect,
  ReactNode,
  FC,
  HTMLAttributes,
} from 'react';
import useInView from '@/hooks/useInView';
import {
  PaginationParams,
  PaginatedResponse,
  OrderArray,
  FilterObject,
  ApiResponse,
} from '@/types';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { QueryStatus } from '@reduxjs/toolkit/query';
import { cn } from '@/lib/utils';
import { useTranslation } from 'react-i18next';
import { X } from 'lucide-react';

interface PaginationState<T = any> {
  params: PaginationParams;
  hasMore: boolean;
  isLoading: boolean;
  error: any;
  loadMore: () => void;
  updateSearch: (search: string) => void;
  updateFilter: (filter: FilterObject) => void;
  updateOrder: (order: OrderArray) => void;
  reset: () => void;
}

interface QueryState<T = any> {
  data?: ApiResponse<PaginatedResponse<T>>;
  isLoading: boolean;
  error?: any;
  refetch: () => void;
  isUninitialized: boolean;
  isFetching: boolean;
  isSuccess: boolean;
  isError: boolean;
  status: QueryStatus;
}

const PaginationContext = createContext<{
  paginationState: PaginationState<any>;
  queryState: QueryState<any>;
} | null>(null);

interface UsePaginationConfig<T> {
  useQueryHook: (params: PaginationParams) => QueryState<T>;
  initialParams?: PaginationParams;
}

const defaultInitialParams = { page: 1, limit: 10 };

export const usePagination = function <T = any>({
  useQueryHook,
  initialParams = defaultInitialParams,
}: UsePaginationConfig<T>) {
  const [params, setParams] = useState<PaginationParams>(initialParams);

  const queryState = useQueryHook(params);
  const { data, isLoading, error } = queryState;

  const loadMore = useCallback(() => {
    if (data?.data?.pagination.hasNext && !isLoading) {
      setParams(prev => ({ ...prev, page: (prev.page ?? 1) + 1 }));
    }
  }, [data?.data?.pagination.hasNext, isLoading]);

  const updateSearch = useCallback((search: string) => {
    setParams(prev => ({ ...prev, search, page: 1 }));
  }, []);

  const updateFilter = useCallback((filter: FilterObject) => {
    setParams(prev => ({ ...prev, filter, page: 1 }));
  }, []);

  const updateOrder = useCallback((order: OrderArray) => {
    setParams(prev => ({ ...prev, order, page: 1 }));
  }, []);

  const reset = useCallback(() => {
    setParams(initialParams);
  }, [initialParams]);

  const paginationState: PaginationState<T> = {
    params,
    hasMore: data?.data?.pagination.hasNext ?? false,
    isLoading,
    error,
    loadMore,
    updateSearch,
    updateFilter,
    updateOrder,
    reset,
  };

  return { paginationState, queryState };
};

interface PaginationProviderProps<T> {
  children: ReactNode;
  value: { paginationState: PaginationState<T>; queryState: QueryState<T> };
}

export const PaginationProvider = function <T>({ children, value }: PaginationProviderProps<T>) {
  return <PaginationContext.Provider value={value}>{children}</PaginationContext.Provider>;
};

export const usePaginationContext = function <T>() {
  const context = useContext(PaginationContext);
  if (!context) {
    throw new Error('usePaginationContext must be used within a PaginationProvider');
  }
  return context as { paginationState: PaginationState<T>; queryState: QueryState<T> };
};

interface PaginationSearchProps
  extends Omit<HTMLAttributes<HTMLInputElement>, 'onChange' | 'value'> {
  placeholder?: string;
  debounceMs?: number;
  onClear?: () => void;
}

export const PaginationSearch: FC<PaginationSearchProps> = ({
  placeholder = 'common.searchEllipsis',
  debounceMs = 300,
  onClear,
  ...props
}) => {
  const { paginationState } = usePaginationContext();
  const { t } = useTranslation();
  const [value, setValue] = useState(paginationState.params.search ?? '');

  useEffect(() => {
    const timer = setTimeout(() => {
      if (value !== paginationState.params.search) {
        paginationState.updateSearch(value);
      }
    }, debounceMs);
    return () => clearTimeout(timer);
  }, [value, debounceMs, paginationState]);

  useEffect(() => {
    setValue(paginationState.params.search ?? '');
  }, [paginationState.params.search]);

  const handleClear = () => {
    setValue('');
    paginationState.updateSearch('');
    onClear?.();
  };

  return (
    <>
      <Input
        value={value}
        onChange={e => setValue(e.target.value)}
        placeholder={t(placeholder)}
        {...props}
      />
      {value && (
        <button
          onClick={handleClear}
          className="absolute right-2 top-1/2 -translate-y-1/2"
          aria-label="clear-search"
        >
          <X className="w-5 h-5 text-secondary-400 hover:text-secondary-600" />
        </button>
      )}
    </>
  );
};

interface PaginationFilterProps extends HTMLAttributes<HTMLDivElement> {
  children?: ReactNode;
}

export const PaginationFilter: FC<PaginationFilterProps> = ({
  children,
  className = '',
  ...props
}) => {
  const { paginationState } = usePaginationContext();
  const { t } = useTranslation();

  if (children) {
    return (
      <div className={className} {...props}>
        {children}
      </div>
    );
  }

  return (
    <div className={cn('text-sm text-tertiary-500', className)} {...props}>
      {t('pagination.defaultFilterUI')}
    </div>
  );
};

interface PaginationLoaderProps extends HTMLAttributes<HTMLDivElement> {
  children?: ReactNode;
}

export const PaginationLoader: FC<PaginationLoaderProps> = ({
  children,
  className = '',
  ...props
}) => {
  const { t } = useTranslation();
  const { paginationState } = usePaginationContext();
  const { ref, inView } = useInView<HTMLDivElement>({ threshold: 0.7 });

  useEffect(() => {
    if (inView && paginationState.hasMore && !paginationState.isLoading) {
      paginationState.loadMore();
    }
  }, [inView, paginationState.hasMore, paginationState.isLoading]);

  const renderChildren = () => {
    if (!paginationState.hasMore) {
      return null;
    }
    return (
      children ?? (
        <div className="flex justify-center items-center">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-500"></div>
          <span className="ml-2 text-tertiary-600">{t('pagination.loadingMore')}</span>
        </div>
      )
    );
  };

  return (
    <div ref={ref} className={cn('text-center py-4', className)} {...props}>
      {renderChildren()}
    </div>
  );
};

interface PaginationEmptyStateProps extends HTMLAttributes<HTMLDivElement> {
  children?: ReactNode;
}

export const PaginationEmptyState: FC<PaginationEmptyStateProps> = ({
  children,
  className = '',
  ...props
}) => {
  const { paginationState, queryState } = usePaginationContext();
  const { t } = useTranslation();
  const items = queryState.data?.data?.items ?? [];

  if (paginationState.isLoading || items.length > 0) {
    return null;
  }

  return (
    <div className={cn('text-center py-8 text-tertiary-500', className)} {...props}>
      {children ?? <p>{t('pagination.noItemsFound')}</p>}
    </div>
  );
};

interface PaginationErrorProps extends HTMLAttributes<HTMLDivElement> {
  children?: ReactNode;
}

export const PaginationError: FC<PaginationErrorProps> = ({
  children,
  className = '',
  ...props
}) => {
  const { paginationState, queryState } = usePaginationContext();
  const { t } = useTranslation();

  if (!paginationState.error) {
    return null;
  }

  return (
    <div className={cn('text-center py-8 text-error-500', className)} {...props}>
      {children ?? (
        <div>
          <p className="font-semibold">{t('pagination.errorLoadingData')}</p>
          <p className="text-sm mt-1 text-error-700">
            {paginationState.error?.message ?? t('common.somethingWrong')}
          </p>
          <Button
            onClick={queryState.refetch}
            variantColor="error"
            variant="outline"
            size="sm"
            className="mt-2"
          >
            {t('pagination.tryAgain')}
          </Button>
        </div>
      )}
    </div>
  );
};

interface PaginationRenderItemsProps<T = any> extends HTMLAttributes<HTMLDivElement> {
  renderItems: (items: T[]) => ReactNode;
}

export const PaginationRenderItems = function <T>({
  renderItems,
  className = '',
  ...props
}: PaginationRenderItemsProps<T>) {
  const { paginationState, queryState } = usePaginationContext<T>();
  const { t } = useTranslation();
  const items = queryState.data?.data?.items ?? [];

  if (paginationState.isLoading && items.length === 0) {
    return (
      <div className={cn('flex justify-center items-center py-8', className)} {...props}>
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"></div>
        <span className="ml-2 text-tertiary-600">{t('common.loading')}</span>
      </div>
    );
  }

  return (
    <div className={className} {...props}>
      {renderItems(items as T[])}
    </div>
  );
};
