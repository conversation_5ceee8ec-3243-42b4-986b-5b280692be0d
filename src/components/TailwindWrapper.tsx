import { cn } from '@/lib/utils';
import React, { forwardRef } from 'react';

interface TailwindWrapperProps {
  children: React.ReactNode;
  className?: string;
  fullscreen?: boolean;
}

const TailwindWrapper = forwardRef<HTMLDivElement, TailwindWrapperProps>(
  ({ children, className, fullscreen = true }, ref) => {
    return (
      <div ref={ref} className={cn('mfe-app', fullscreen && 'h-full w-full', className)}>
        {children}
      </div>
    );
  }
);

export default TailwindWrapper;
