import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import {
  usePagination,
  PaginationProvider,
  usePaginationContext,
  PaginationSearch,
  PaginationLoader,
  PaginationEmptyState,
  PaginationError,
  PaginationRenderItems,
} from '../Pagination';

// Mock react-i18next
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => {
      const translations: Record<string, string> = {
        'common.searchEllipsis': 'Search...',
        'common.loading': 'Loading...',
        'pagination.loadingMore': 'Loading more...',
        'pagination.noItemsFound': 'No items found',
        'pagination.errorLoadingData': 'Error loading data',
        'pagination.tryAgain': 'Try Again',
        'common.somethingWrong': 'Something went wrong',
        'pagination.defaultFilterUI': 'Default Filter UI',
      };
      return translations[key] || key;
    },
  }),
}));

// Mock useInView hook
vi.mock('@/hooks/useInView', () => ({
  default: vi.fn(() => ({ ref: { current: null }, inView: false })),
}));

// Mock UI components
vi.mock('@/components/ui/input', () => ({
  Input: ({ value, onChange, placeholder, ...props }: any) => (
    <input
      data-testid="search-input"
      value={value}
      onChange={onChange}
      placeholder={placeholder}
      {...props}
    />
  ),
}));

vi.mock('@/components/ui/button', () => ({
  Button: ({ children, onClick, className, variant, size, variantColor, ...props }: any) => (
    <button
      data-testid="button"
      onClick={onClick}
      className={className}
      data-variant={variant}
      data-size={size}
      data-variant-color={variantColor}
      {...props}
    >
      {children}
    </button>
  ),
}));

// Mock lucide-react
vi.mock('lucide-react', () => ({
  X: ({ className }: any) => (
    <div data-testid="x-icon" className={className} />
  ),
}));

// Mock cn utility
vi.mock('@/lib/utils', () => ({
  cn: (...classes: string[]) => classes.filter(Boolean).join(' '),
}));

describe('Pagination Components', () => {
  const mockQueryHook = vi.fn();
  const mockRefetch = vi.fn();

  const createMockQueryState = (overrides = {}) => ({
    data: {
      success: true,
      timestamp: new Date().toISOString(),
      data: {
        items: [{ id: 1, name: 'Item 1' }, { id: 2, name: 'Item 2' }],
        pagination: {
          hasNext: true,
          hasPrev: false,
          page: 1,
          limit: 10,
          total: 20,
          totalPages: 2,
        },
      },
    },
    isLoading: false,
    error: null,
    refetch: mockRefetch,
    isUninitialized: false,
    isFetching: false,
    isSuccess: true,
    isError: false,
    status: 'fulfilled' as any,
    ...overrides,
  } as any);

  beforeEach(() => {
    vi.clearAllMocks();
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  describe('usePagination Hook', () => {
    it('should initialize with default params', () => {
      const mockQuery = createMockQueryState();
      mockQueryHook.mockReturnValue(mockQuery);

      const TestComponent = () => {
        const { paginationState } = usePagination({ useQueryHook: mockQueryHook });
        return <div data-testid="test">{JSON.stringify(paginationState.params)}</div>;
      };

      render(<TestComponent />);

      expect(screen.getByTestId('test')).toHaveTextContent('{"page":1,"limit":10}');
    });

    it('should handle loadMore', () => {
      const mockQuery = createMockQueryState();
      mockQueryHook.mockReturnValue(mockQuery);

      const TestComponent = () => {
        const { paginationState } = usePagination({ useQueryHook: mockQueryHook });
        return (
          <div>
            <button data-testid="load-more" onClick={paginationState.loadMore}>
              Load More
            </button>
            <div data-testid="has-more">{paginationState.hasMore.toString()}</div>
          </div>
        );
      };

      render(<TestComponent />);

      expect(screen.getByTestId('has-more')).toHaveTextContent('true');
      
      fireEvent.click(screen.getByTestId('load-more'));
      // The page should increase but we can't easily test state changes in this setup
      expect(screen.getByTestId('load-more')).toBeInTheDocument();
    });
  });

  describe('PaginationProvider', () => {
    it('should provide context to children', () => {
      const mockState = {
        paginationState: {
          params: { page: 1, limit: 10 },
          hasMore: false,
          isLoading: false,
          error: null,
          loadMore: vi.fn(),
          updateSearch: vi.fn(),
          updateFilter: vi.fn(),
          updateOrder: vi.fn(),
          reset: vi.fn(),
        },
        queryState: createMockQueryState(),
      };

      const TestChild = () => {
        const { paginationState } = usePaginationContext();
        return <div data-testid="context-test">{paginationState.params.page}</div>;
      };

      render(
        <PaginationProvider value={mockState}>
          <TestChild />
        </PaginationProvider>
      );

      expect(screen.getByTestId('context-test')).toHaveTextContent('1');
    });
  });

  describe('PaginationSearch', () => {
    it('should render search input with placeholder', () => {
      const mockState = {
        paginationState: {
          params: { page: 1, limit: 10, search: '' },
          updateSearch: vi.fn(),
        } as any,
        queryState: createMockQueryState(),
      };

      render(
        <PaginationProvider value={mockState}>
          <PaginationSearch />
        </PaginationProvider>
      );

      expect(screen.getByTestId('search-input')).toBeInTheDocument();
      expect(screen.getByTestId('search-input')).toHaveAttribute('placeholder', 'Search...');
    });

    it('should show clear button when there is a value', () => {
      const mockState = {
        paginationState: {
          params: { page: 1, limit: 10, search: 'test' },
          updateSearch: vi.fn(),
        } as any,
        queryState: createMockQueryState(),
      };

      render(
        <PaginationProvider value={mockState}>
          <PaginationSearch />
        </PaginationProvider>
      );

      expect(screen.getByLabelText('clear-search')).toBeInTheDocument();
      expect(screen.getByTestId('x-icon')).toBeInTheDocument();
    });

    it('should handle clear button click', () => {
      const mockUpdateSearch = vi.fn();
      const mockOnClear = vi.fn();
      
      const mockState = {
        paginationState: {
          params: { page: 1, limit: 10, search: 'test' },
          updateSearch: mockUpdateSearch,
        } as any,
        queryState: createMockQueryState(),
      };

      render(
        <PaginationProvider value={mockState}>
          <PaginationSearch onClear={mockOnClear} />
        </PaginationProvider>
      );

      fireEvent.click(screen.getByLabelText('clear-search'));

      expect(mockUpdateSearch).toHaveBeenCalledWith('');
      expect(mockOnClear).toHaveBeenCalled();
    });
  });

  describe('PaginationLoader', () => {
    it('should render loading spinner when hasMore is true', () => {
      const mockState = {
        paginationState: {
          hasMore: true,
          isLoading: false,
          loadMore: vi.fn(),
        } as any,
        queryState: createMockQueryState(),
      };

      render(
        <PaginationProvider value={mockState}>
          <PaginationLoader />
        </PaginationProvider>
      );

      expect(screen.getByText('Loading more...')).toBeInTheDocument();
      expect(document.querySelector('.animate-spin')).toBeInTheDocument();
    });

    it('should not render when hasMore is false', () => {
      const mockState = {
        paginationState: {
          hasMore: false,
          isLoading: false,
          loadMore: vi.fn(),
        } as any,
        queryState: createMockQueryState(),
      };

      const { container } = render(
        <PaginationProvider value={mockState}>
          <PaginationLoader />
        </PaginationProvider>
      );

      expect(container.firstChild?.textContent).toBe('');
    });
  });

  describe('PaginationEmptyState', () => {
    it('should render when no items and not loading', () => {
      const mockState = {
        paginationState: {
          isLoading: false,
        } as any,
        queryState: createMockQueryState({
          data: { data: { items: [] } },
        }),
      };

      render(
        <PaginationProvider value={mockState}>
          <PaginationEmptyState />
        </PaginationProvider>
      );

      expect(screen.getByText('No items found')).toBeInTheDocument();
    });

    it('should not render when items exist', () => {
      const mockState = {
        paginationState: {
          isLoading: false,
        } as any,
        queryState: createMockQueryState(),
      };

      const { container } = render(
        <PaginationProvider value={mockState}>
          <PaginationEmptyState />
        </PaginationProvider>
      );

      expect(container.firstChild).toBeNull();
    });
  });

  describe('PaginationError', () => {
    it('should render error message when error exists', () => {
      const mockState = {
        paginationState: {
          error: { message: 'Network error' },
        } as any,
        queryState: createMockQueryState({ refetch: mockRefetch }),
      };

      render(
        <PaginationProvider value={mockState}>
          <PaginationError />
        </PaginationProvider>
      );

      expect(screen.getByText('Error loading data')).toBeInTheDocument();
      expect(screen.getByText('Network error')).toBeInTheDocument();
      expect(screen.getByText('Try Again')).toBeInTheDocument();
    });

    it('should call refetch when try again is clicked', () => {
      const mockState = {
        paginationState: {
          error: { message: 'Error' },
        } as any,
        queryState: createMockQueryState({ refetch: mockRefetch }),
      };

      render(
        <PaginationProvider value={mockState}>
          <PaginationError />
        </PaginationProvider>
      );

      fireEvent.click(screen.getByText('Try Again'));
      expect(mockRefetch).toHaveBeenCalled();
    });

    it('should not render when no error', () => {
      const mockState = {
        paginationState: {
          error: null,
        } as any,
        queryState: createMockQueryState(),
      };

      const { container } = render(
        <PaginationProvider value={mockState}>
          <PaginationError />
        </PaginationProvider>
      );

      expect(container.firstChild).toBeNull();
    });
  });

  describe('PaginationRenderItems', () => {
    it('should render items using renderItems function', () => {
      const mockState = {
        paginationState: {
          isLoading: false,
        } as any,
        queryState: createMockQueryState(),
      };

      const renderItems = (items: any[]) => (
        <div data-testid="items-list">
          {items.map(item => (
            <div key={item.id} data-testid="item">
              {item.name}
            </div>
          ))}
        </div>
      );

      render(
        <PaginationProvider value={mockState}>
          <PaginationRenderItems renderItems={renderItems} />
        </PaginationProvider>
      );

      expect(screen.getByTestId('items-list')).toBeInTheDocument();
      expect(screen.getAllByTestId('item')).toHaveLength(2);
      expect(screen.getByText('Item 1')).toBeInTheDocument();
      expect(screen.getByText('Item 2')).toBeInTheDocument();
    });

    it('should show loading when items are empty and loading', () => {
      const mockState = {
        paginationState: {
          isLoading: true,
        } as any,
        queryState: createMockQueryState({
          data: { data: { items: [] } },
          isLoading: true,
        }),
      };

      render(
        <PaginationProvider value={mockState}>
          <PaginationRenderItems renderItems={() => null} />
        </PaginationProvider>
      );

      expect(screen.getByText('Loading...')).toBeInTheDocument();
      expect(document.querySelector('.animate-spin')).toBeInTheDocument();
    });
  });
});