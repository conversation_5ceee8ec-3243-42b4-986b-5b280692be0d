import React from 'react';
import { Select, SelectTrigger, SelectContent, SelectItem, SelectValue } from './ui/select';
import { cn } from '@/lib/utils';
import { DropdownOption } from '@/types';
import { useTranslation } from 'react-i18next';

export interface DropdownButtonProps<T> {
  value: T;
  onChange: (value: T) => void;
  options: DropdownOption[];
  icon?: React.ReactNode;
  className?: string;
  triggerClassName?: string;
  placeholder?: string;
  disabled?: boolean;
}

const DropdownButton = <T extends string = string>({
  value,
  onChange,
  options,
  icon,
  className,
  triggerClassName,
  placeholder,
  disabled,
}: DropdownButtonProps<T>) => {
  const { t } = useTranslation();
  const defaultPlaceholder = placeholder ?? t('common.selectOption');
  return (
    <div className={cn('relative', className)}>
      <Select value={value} onValueChange={onChange} disabled={disabled}>
        <SelectTrigger
          className={cn(
            'px-4 py-1.5 border-none rounded-lg text-sm min-w-24 shadow-none focus:ring-0 focus:ring-offset-0 bg-tertiary-100 focus:outline-none gap-1',
            triggerClassName
          )}
        >
          {icon}
          <SelectValue placeholder={defaultPlaceholder} />
        </SelectTrigger>
        <SelectContent>
          {options?.map(option => (
            <SelectItem className="flex gap-1" key={option.value} value={option.value}>
              <div className="flex gap-1 items-center">
                {option.icon && (
                  <div className="w-6 h-6 flex items-center justify-center">{option.icon}</div>
                )}
                {option.label}
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
};

export default DropdownButton;
