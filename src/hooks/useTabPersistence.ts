import { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';

interface UseTabPersistenceOptions {
  defaultTab: string;
  queryParamName?: string;
}

export const useTabPersistence = ({
  defaultTab,
  queryParamName = 'tab',
}: UseTabPersistenceOptions) => {
  const [searchParams, setSearchParams] = useSearchParams();
  const urlTab = searchParams.get(queryParamName);

  const [activeTab, setActiveTab] = useState<string>(urlTab || defaultTab);

  useEffect(() => {
    if (activeTab !== urlTab) {
      setSearchParams(prev => {
        const params = new URLSearchParams(prev);
        params.set(queryParamName, activeTab);
        return params;
      });
    }
  }, [activeTab, urlTab, queryParamName, setSearchParams]);

  return [activeTab, setActiveTab] as const;
};
