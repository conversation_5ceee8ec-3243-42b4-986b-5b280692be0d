import { useTranslation } from 'react-i18next';
import { z } from 'zod';
import i18n from 'i18next';

export const isValidURL = (str: string): boolean => {
  if (!str) {
    return false;
  }

  // Ensure the URL starts with http:// or https://
  const protocolRegex = /^(https?:\/\/)/i;
  if (!protocolRegex.test(str)) {
    return false;
  }

  // Blacklist of protocols that can be used for XSS attacks.
  const DANGEROUS_PROTOCOLS = ['javascript:', 'vbscript:', 'data:'];

  try {
    const url = new URL(str);
    return !DANGEROUS_PROTOCOLS.includes(url.protocol);
  } catch {
    return false;
  }
};

export const useURLValidator = () => {
  const { t } = useTranslation();

  const validateURL = (str: string): [boolean, string] => {
    if (!str) {
      return [false, t('BLANK_URL')];
    }
    if (str.length > 500) {
      return [false, t('URL_TOO_LONG')];
    }

    if (!isValidURL(str)) {
      return [false, t('INVALID_URL')];
    }

    return [true, ''];
  };

  return { validateURL };
};

export const displayTextSchema = z.string().trim().max(100, i18n.t('URL_DISPLAY_TEXT_TOO_LONG'));
