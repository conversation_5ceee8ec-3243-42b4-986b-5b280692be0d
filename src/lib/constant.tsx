import React from 'react';
import flag from '@/assets/icons/flag-uk.svg';
import { DateFilter, StatusFilter } from '../types/enums/enums';
import whatsapp from '@/assets/icons/WhatsApp.png';
import { Globe, Star, TabletSmartphone, ThumbsDown, ThumbsUp } from 'lucide-react';

export const RoutesName = {
  HOME: '/app/neuratalk',
  NEURA_TALK_BUILDER: '/app/neuratalk-builder/:botId',
  NEURA_TALK_BUILDER_TAB: '/app/neuratalk-builder/:botId/:tabId',
};

export const getNTBTab = (botId: string, tabId: string) => {
  return RoutesName.NEURA_TALK_BUILDER_TAB.replace(':botId', botId).replace(':tabId', tabId);
};

export const LoadingText = {
  LOADING_1: 'loading.hangTight',
  LOADING_2: 'loading.almostThere',
};

export enum Language {
  English = 'english',
  Spanish = 'spanish',
  French = 'french',
}

export const languageOptions = [
  {
    value: Language.English,
    label: 'English',
    icon: <img src={flag} alt="UK-flag" className="w-6 h-6" />,
  },
  // {
  //   value: Language.Spanish,
  //   label: 'Spanish',
  //   icon: <img src={flag} alt="UK-flag" className="w-6 h-6" />,
  // },
  // {
  //   value: Language.French,
  //   label: 'French',
  //   icon: <img src={flag} alt="UK-flag" className="w-6 h-6" />,
  // },
];

export const dateOptions = Object.values(DateFilter).map(value => ({
  label: value,
  value,
}));

export const statusOption = Object.values(StatusFilter).map(value => ({
  label: value,
  value,
}));

export enum PlatformType {
  Web = 'web',
  WhatsApp = 'whatsapp',
  Mobile = 'mobile',
}

export const platformOptions = [
  { value: PlatformType.Web, label: 'Web', icon: <Globe className="w-5 h-5 text-tertiary-600" /> },
  {
    value: PlatformType.Mobile,
    label: 'Mobile',
    icon: <TabletSmartphone className="w-5 h-5 text-tertiary-600" />,
  },
  {
    value: PlatformType.WhatsApp,
    label: 'WhatsApp',
    icon: <img src={whatsapp} alt="whatsapp" className="w-6 h-6" />,
  },
];

export const feedbackOptions = [
  {
    value: 'Star',
    label: 'Star Rating',
    icon: (
      <div className="flex">
        {Array.from({ length: 5 }).map((_, i) => (
          <Star key={`star-${i}`} className="w-5 h-5 text-tertiary-500" />
        ))}
      </div>
    ),
  },
  {
    value: 'Thumbs',
    label: 'Thumbs',
    icon: (
      <div className="flex">
        {' '}
        <ThumbsUp className="w-5 h-5 text-tertiary-500" />{' '}
        <ThumbsDown className="w-5 h-5 text-tertiary-500" />{' '}
      </div>
    ),
  },
  {
    value: 'Text',
    label: 'Text',
    icon: (
      <div className="flex gap-1 text-tertiary-500">
        {Array.from({ length: 5 }, (_, i) => (
          <span key={`text-${i}`}>{i + 1}</span>
        ))}
      </div>
    ),
  },
];
