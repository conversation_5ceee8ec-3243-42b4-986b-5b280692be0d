{"common": {"search": "ب<PERSON><PERSON>", "filter": "تصفية", "create": "إنشاء", "save": "<PERSON><PERSON><PERSON>", "submit": "إرسال", "cancel": "إلغاء", "delete": "<PERSON><PERSON><PERSON>", "add": "إضافة", "clone": "استنساخ", "export": "تصدير", "edit": "تعديل", "yes": "نعم", "no": "لا", "selectOption": "اختر خيارًا", "getStarted": "ابد<PERSON>", "preview": "معاينة", "publish": "نشر", "duplicate": "تكرار", "versionHistory": "سجل الإصدارات", "flows": "التدفقات", "debugger": "م<PERSON><PERSON><PERSON> الأخطاء", "message": "رسالة", "image": "صورة", "file": "مل<PERSON>", "video": "فيديو", "addViaUrl": "إضافة عبر رابط", "enterFileUrl": "أدخل رابط الملف", "maxSize": "الحجم الأقصى: {{size}} ميغابايت", "clickOrDrag": "انقر أو اسحب الملف {{type}} هنا", "clickOrDragFiles": "انقر أو اسحب الملف إلى هذه المنطقة للتحميل", "writeMessage": "كتابة رسالة", "typeMessage": "اكتب رسالتك...", "fillAboveField": "املأ النموذج أعلاه للمتابعة", "dateRange": "اختر نطاق التواريخ", "trackOrder": "تتبع طلبي", "cancelOrder": "إلغاء طلبي", "chatWithAgent": "الدردشة مع وكيل", "viewSimilarProducts": "عرض المنتجات المشابهة", "hello": "مرحبًا، {{name}}!", "howCanIHelp": "كيف يمكنني مساعدتك اليوم؟", "searchFlows": "البحث عن التدفقات...", "onboarding": "التأهيل الأولي", "notFound": "<PERSON>ير موجود", "enterValidValue": "ير<PERSON>ى إدخال قيمة صالحة", "translateTo": "ترجم إلى", "translate": "ترجم", "nothingToShow": "لا يوجد شيء لعرضه", "generate": "إنشاء", "close": "إغلاق", "nodeId": "معرف العقدة:", "noData": "لا توجد بيانات", "searchEllipsis": "بحث...", "justNow": "الآن", "update": "تحديث", "error": "خطأ", "somethingWrong": "حد<PERSON> خطأ ما", "offlineError": "أنت غير متصل حاليًا. يرجى التحقق من اتصالك بالإنترنت.", "saveChanges": "حفظ التغييرات", "date": "تاريخ", "loading": "جارٍ التحميل...", "build": "بناء", "import": "استيراد", "share": "مشاركة", "goBack": "العودة", "noNode": "لم يتم العثور على تطابق", "versions": "الإصدارات", "intent": "نية"}, "chatbot": {"botPublishedSuccessfully": "تم نشر البوت بنجاح!", "untitled": "بدون عنوان", "noDomain": "افتراضي", "noDescription": "بدون وصف", "confirmDelete": "تأكيد الحذف", "deleteMessage": "هل أنت متأكد أنك تريد حذف هذا الروبوت؟", "chatbotDeleted": "تم حذف الروبوت بنجاح", "noCancel": "لا، إلغاء", "yesDelete": "نعم، حذف", "newChatbotPrefix": "روبوت-جديد", "cloneFailed": "فشل استنساخ الروبوت.", "buildSuccess": "تم بناء الروبوت بنجاح!", "buildFailed": "فشل بناء الروبوت.", "buildStreamError": "خطأ في الاتصال بتدفق البناء.", "defaultTitle": "روبوتي", "defaultDomain": "تجارة إلكترونية", "defaultDescription": "يساعد العملاء في التنقل عبر عملية الشراء الرقمية.", "createChatbot": "تم إنشاء شات بوت جديد بنجاح"}, "debugger": {"logs": "السجلات", "aiAnalysis": "تحليل الذكاء الاصطناعي", "sessionData": "بيانات الجلسة", "aiAnalysisContent": "محتوى تحليل الذكاء الاصطناعي قريبًا.", "sessionDataContent": "محتوى بيانات الجلسة قريبًا.", "noAiAnalysisLogs": "لا توجد سجلات تحليل الذكاء الاصطناعي متاحة.", "noSessionDataLogs": "لا توجد سجلات بيانات الجلسة متاحة.", "noLogs": "لا توجد سجلات متاحة."}, "preview": {"confirmDialog": "هل ترغب في إنهاء هذه المحادثة؟", "confirmDialogDesc": "سيؤدي هذا إلى محو الدردشة وإغلاق النافذة"}, "validation": {"maxLength": "لا يمكن أن يتجاوز هذا الحقل {{count}} حرفًا.", "BLANK_URL": "لا يمكن أن يكون الرابط فارغًا.", "URL_TOO_LONG": "الرابط طويل جدًا.", "INVALID_URL": "تنسيق الرابط غير صالح.", "URL_DISPLAY_TEXT_TOO_LONG": "لا يمكن أن يتجاوز النص المعروض 100 حرف.", "invalidPhoneNumber": "رقم هاتف غير صالح لـ {{field}}.", "fieldRequired": "{{field}} مطلوب.", "invalidEmail": "بريد إلكتروني غير صالح لـ {{field}}.", "passwordMinLength": "يجب أن تتكون كلمة المرور لـ {{field}} من 6 أحرف على الأقل.", "invalidTimeFormat": "تنسيق الوقت غير صالح لـ {{field}}.", "invalidDate": "تاريخ غير صالح لـ {{field}}.", "pastDateRequired": "يجب أن يكون {{field}} تاريخًا سابقًا (قبل {{date}})", "futureDateRequired": "يجب أن يكون {{field}} تاريخًا مستقبليًا (بعد {{date}})", "invalidNumber": "رقم غير صالح لـ {{field}}"}, "home": {"title": "نيورا توك للذكاء الاصطناعي", "description": "هو حل ذكاء اصطناعي محادثاتي متطور مصمم لتحسين تفاعل العملاء، وأتمتة الدعم، وتبسيط العمليات التجارية.", "noResults": "لا توجد نتائج", "lastUpdated": "آخر تحديث في {{date}}"}, "editor": {"chatbotName": "اسم الروبوت", "domain": "المجال", "description": "الوصف", "uploadImage": "انقر أو اسحب ملفًا إلى هذه المنطقة للتحميل", "uploadFormat": "(الحجم: حتى 5 ميغابايت | التنسيق: jpg، png)", "unsupportedFile": "نوع الملف غير مدعوم", "fileTooLarge": "يجب أن يكون حجم الملف أقل من 2 ميغابايت", "invalidName": "يُسمح فقط بالحروف، والأرقام، والواصلات (-)، والتسطير (_)، والنقاط (.)", "invalidImageFile": "يرجى تحميل ملف صورة صالح (png، jpg، jpeg، webp، gif، svg)", "nameRequired": "اسم الروبوت مطلوب", "nameMaxError": "لا يمكن أن يتجاوز اسم الروبوت 50 حرفًا", "domainRequired": "المجال مطلوب", "descMaxError": "لا يمكن أن يتجاوز الوصف 150 حرفًا", "updateSuccess": "تم تحديث Chatbot بنجاح", "descRequired": "الوصف مطلوب", "updateError": "فشل تحديث البوت", "writeMessage": "كتابة رسالة"}, "navigation": {"neuraTalk": "نيورا توك", "create": "إنشاء"}, "domains": {"it": "هو - هي", "ecomm": "تجارة إلكترونية", "telecom": "الاتصالات", "retail": "تجزئة", "travel": "سفر", "other": "أ<PERSON><PERSON><PERSON>"}, "emptyState": {"title": "لا يوجد شيء هنا بعد", "description": "لا يوجد محتوى لعرضه حاليًا."}, "intents": {"title": "النوايا", "addTitle": "إضافة نية", "editTitle": "تعديل نية", "name": "اسم النية", "namePlaceholder": "اسم النية", "nameLabel": "اسم النية", "nameRequired": "اسم النية مطلوب.", "startAdding": "ابدأ بإضافة النوايا", "noFlowsConnected": "لا توجد تدفقات متصلة", "selectToManage": "اختر نية لإدارة التعبيرات", "loading": "جارٍ تحميل النوايا.", "loadingError": "خطأ أثناء تحميل النوايا.", "intentAdded": "تمت إضافة النية بنجاح.", "intentUpdated": "تم تحديث النية بنجاح.", "intentDeleted": "تم حذف النية بنجاح.", "confirmDeleteTitle": "تأ<PERSON>يد حذف النية", "deleteConfirmationMessage": "هل أنت متأكد أنك تريد حذف هذه النية؟", "utterances": {"title": "التعبيرات", "addTitle": "إضافة تعبير", "editTitle": "تعديل تعبير", "enterPlaceholder": "أد<PERSON>ل التعبير", "startAdding": "ابدأ بإضافة التعبيرات", "emptyError": "لا يمكن أن يكون التعبير فارغًا.", "loading": "جارٍ تحميل التعبيرات.", "loadingError": "خطأ أثناء تحميل التعبيرات.", "utteranceAdded": "تمت إضافة التعبير.", "utteranceUpdated": "تم تحديث التعبير.", "utteranceDeleted": "تم حذف التعبير.", "confirmDeleteTitle": "تأ<PERSON>يد حذف التعبير", "deleteConfirmationMessage": "هل أنت متأكد أنك تريد حذف هذا التعبير؟"}}, "entities": {"title": "الكيانات", "addTitle": "إضافة كيان", "entityName": "اسم الكيان", "entityNamePlaceholder": "اسم الكيان", "type": "النوع", "selectType": "اختر النوع", "enablePartialMatch": "تمكين التطابق الجزئي", "startAdding": "ابد<PERSON> بإضافة الكيانات", "loading": "جارٍ تحميل الكيانات...", "error": "خطأ أثناء تحميل الكيانات.", "noEntitiesFound": "لم يتم العثور على كيانات", "searchEntities": "البحث عن الكيانات...", "selected": "<PERSON><PERSON><PERSON><PERSON>", "removeEntity": "إزالة الكيان", "types": {"text": "نص", "list": "قائمة", "regex": "تعبير عادي"}, "table": {"name": "الاسم", "type": "النوع", "value": "القيمة", "action": "الإجراء"}, "validation": {"nameRequired": "اسم الكيان مطلوب.", "typeRequired": "نوع الكيان مطلوب.", "valueRequired": "القيمة مطلوبة."}, "addValue": "إضافة قيمة", "editTitle": "تعديل كيان", "regexValuePlaceholder": "قيمة التعبير العادي", "entityAdded": "تمت إضافة الكيان بنجاح.", "entityUpdated": "تم تحديث الكيان بنجاح.", "entityDeleted": "تم حذف الكيان بنجاح.", "confirmDeleteTitle": "ت<PERSON>كيد حذف الكيان", "deleteConfirmationMessage": "هل أنت متأكد أنك تريد حذف هذا الكيان؟"}, "train": {"entities": {"title": "الكيانات", "content": "محتوى الكيانات", "addTitle": "إضافة كيان", "nameLabel": "اسم الكيان", "intentIdLabel": "معرف النية", "metadataLabel": "البيانات الوصفية (JSON)", "metadataPlaceholder": "أدخل البيانات الوصفية بتنسيق JSON", "loading": "جارٍ تحميل الكيانات...", "error": "خطأ أثناء تحميل الكيانات.", "validation": {"nameRequired": "اسم الكيان مطلوب.", "intentIdRequired": "معرف النية مطلوب.", "invalidJson": "تنسيق JSON غير صالح للبيانات الوصفية."}}, "synonyms": {"title": "المرادفات", "content": "محتوى المرادفات"}, "smallTalk": {"title": "المحادثة العامة", "content": "محتوى المحادثة العامة"}, "trainFromLogs": {"title": "التدريب من السجلات", "content": "محتوى التدريب من السجلات"}, "tabs": {"intentUtterances": "تعبيرات النية", "entities": "الكيانات", "faqs": "الأسئلة الشائعة", "synonyms": "المرادفات", "smallTalk": "المحادثة العامة", "trainFromLogs": "التدريب من السجلات"}}, "faqs": {"title": "الأسئلة والأجوبة", "category": {"title": "الفئة", "addTitle": "إضافة فئة", "editTitle": "تعديل فئة", "nameLabel": "اسم الفئة", "nameRequired": "اسم الفئة مطلوب.", "startAdding": "ابدأ بإضافة الفئات", "selectToManage": "اختر فئة لإدارة الأسئلة", "categoryAdded": "تمت إضافة الفئة بنجاح.", "categoryUpdated": "تم تحديث الفئة بنجاح.", "categoryDeleted": "تم حذف الفئة بنجاح.", "confirmDeleteTitle": "تأ<PERSON>يد حذف الفئة", "deleteConfirmationMessage": "هل أنت متأكد أنك تريد حذف هذه الفئة؟"}, "loading": "جارٍ تحميل الأسئلة الشائعة.", "loadingError": "خطأ أثناء تحميل الأسئلة الشائعة.", "items": {"loading": "جارٍ تحميل عناصر الأسئلة الشائعة...", "loadingError": "خطأ أثناء تحميل عناصر الأسئلة الشائعة.", "startAdding": "ابدأ بإضافة الأسئلة", "addTitle": "إضافة سؤال", "editTitle": "تعديل سؤال", "questionLabel": "الأسئلة", "questionPlaceholder": "أدخل السؤال", "questionEmpty": "لا يمكن أن يكون السؤال فارغًا.", "atLeastOne": "مطلوب سؤال واح<PERSON> على الأقل.", "answerLabel": "الإجابة", "answerPlaceholder": "أد<PERSON>ل الإجابة", "answerEmpty": "لا يمكن أن تكون الإجابة فارغة.", "linkFlowLabel": "ربط التدفق", "chooseFlowPlaceholder": "اختر التدفق", "primaryLabel": "أساسي", "questionPrefix": "س", "answerPrefix": "ج", "questionsAdded": "تمت إضافة الأسئلة.", "questionsUpdated": "تم تحديث الأسئلة.", "maxQuestions": "يمكنك إضافة ما يصل إلى {{count}} أسئلة.", "questionsDeleted": "تم حذف الأسئلة.", "confirmDeleteTitle": "تأ<PERSON>يد حذف الأسئلة الشائعة", "deleteConfirmationMessage": "هل أنت متأكد أنك تريد حذف هذا العنصر من الأسئلة الشائعة؟", "limitQuestionError": "تم الوصول إلى الحد الأقصى. لا يمكن أن تحتوي الأسئلة الشائعة على أكثر من ١٠ أسئلة."}, "validation": {"questionRequired": "السؤال مطلوب.", "atLeastOneQuestion": "مطلوب سؤال واح<PERSON> على الأقل.", "answerRequired": "الإجابة مطلوبة."}}, "fileUpload": {"fileTooLarge": "يجب أن يكون حجم الملف أقل من {{size}} ميغابايت ومن نوع {{type}}", "someFilesRejected": "تم رفض بعض الملفات. تأكد من أن الأنواع صحيحة والحجم أقل من {{size}} ميغابايت.", "failedToUpload": "فشل التحميل: {{filename}}"}, "tabs": {"contentComingSoon": "محتوى التبويب {{tabName}} قريبًا"}, "builder": {"tabs": {"design": "تصميم", "train": "تدريب", "channels": "القنوات", "agentTransfer": "نقل الوكيل", "integrations": "التكاملات", "settings": "الإعدادات"}}, "flows": {"untitledFlow": "تدفق-بدون-عنوان", "noFlows": "لم يتم العثور على تطابق", "welcome": "مرحبًا", "fallback": "الاحتياطي", "targetFlow": "التدفق المستهدف", "existingFlow": "تذكر السياق بين تدفق الاتصال", "errorLoading": "خطأ أثناء تحميل التدفقات", "newFlow": "تد<PERSON><PERSON> جديد", "fetchError": "فشل جلب التدفقات", "flowDelete": "تم حذف التد<PERSON>ق بنجاح", "flowNotCreated": "تعذّر إنشاء التدفق", "flowDeleted": "تم حذف التد<PERSON>ق بنجاح", "flowNotDeleted": "تعذّر حذف التدفق", "flowDuplicated": "تم تكرار التدفق بنجاح", "flowNotDuplicated": "تعذّر تكرار التدفق", "flowRenamed": "تمت إعادة تسمية التدفق بنجاح", "flowNotRenamed": "تعذّر إعادة تسمية التدفق", "confirmDelete": "تأكيد الحذف", "deleteConfirmationMessage": "هل أنت متأكد أنك تريد حذف التدفق ؟"}, "agentTransfer": {"transfer": "دمج الوكيل من صفحة 'نقل الوكيل' لتكوين الوكيل الأصلي", "selectAgentTransfer": "اختر نقل وكيل", "nothingSelected": "لم يتم تحديد شيء", "filters": {"all": "الكل", "native": "أصلي", "thirdParty": "جهة خارجية"}, "tabs": {"available": "متاح", "myAgentTransfers": "نقل الوكلاء الخاص بي"}, "setupHeading": "إعداد", "setupDescription": "قدم التفاصيل أدناه لتفعيل دعم {{agentTransferName}} للروبوت.", "generateToken": "إنشاء رمز", "liveAgentPortalDetails": "تفاصيل بوابة الوكيل المباشر", "pendingStatus": "قيد الانتظار", "remove": "إزالة", "chatbotNameLabel": "اسم الروبوت:", "accessTokenLabel": "رمز الوصول:", "shareInstruction": "شارك ما سبق مع مدير الوكيل للتفعيل."}, "settings": {"language": "اللغة", "nlu": "معالجة اللغة الطبيعية", "personalization": "التخصيص", "llmConfiguration": "تكوين نموذج اللغة الكبير", "cannedResponses": "الردود الجاهزة", "loremDescription": "لوريم إيبسوم دولور سيت أميت، كونسكتيتور أديبيسكينغ إليت، سيد دو إيوسمود تيمبور", "languages": "اللغات", "yourLanguages": "لغاتك", "noLanguagesSelected": "لم يتم تحديد أي لغات", "availableLanguages": "اللغات المتاحة", "searchLanguages": "البحث عن اللغات...", "defaultTag": "افتراضي", "allSelectedLanguages": "جميع اللغات المختارة", "languagesSaved": "تم حفظ اللغات بنجاح", "languageDisabledSuccessfully": "تم تعطيل اللغة بنجاح", "disableLanguageConfirmationTitle": "تعطيل اللغة", "disableLanguageConfirmationMessage": "هل أنت متأكد أنك تريد تعطيل هذه اللغة من الروبوت؟"}, "stencil": {"nodes": "العقد", "searchNodes": "البحث عن العقد...", "engage": "إشراك", "utilities": "الأدوات", "marketplace": "السوق"}, "platform": {"web": "ويب", "mobile": "موبايل"}, "pagination": {"previous": "السابق", "next": "التالي", "morePages": "المزيد من الصفحات", "loadingMore": "جارٍ تحميل المزيد...", "noItemsFound": "لم يتم العثور على عناصر", "errorLoadingData": "خطأ أثناء تحميل البيانات", "tryAgain": "حاول مرة أخرى", "defaultFilterUI": "واجهة تصفية افتراضية – قابلة للتخصيص مع خصائص الأطفال"}, "form": {"loadingForm": "جارٍ تحميل النموذج...", "typing": "جارٍ الكتابة...", "enterLabel": "أد<PERSON>ل التسمية", "prompt": "أد<PERSON>ل موجه", "textField": "حقل نص", "label": "تسمية", "required": "مطلوب", "lableRequired": "الحقل مطلوب", "promptRequired": "مطلوب موجه"}, "errors": {"failedToSend": "فشل إرسال الرسالة", "unexpectedResponse": "استجابة غير متوقعة من الخادم", "somethingWrong": "حد<PERSON> خطأ ما"}, "channels": {"selectWABA": "اختر رقم WABA للاتصال", "changeNumber": "تغيير الرقم", "webhook": "ويبهوك", "webhookInstruction": "الصق هذا الويبهوك مقابل رقم WABA في قناة NGAGE WhatsApp للتكامل.", "switchToMeta": "التبديل إلى Meta Cloud API", "switchDescription": "انتقل إلى Meta Cloud API وربط روبوتك عبر شريك BSP.", "switch": "تبديل", "connect": "اتصال", "selectChannels": "اختر القنوات للتكوين", "nothingSelected": "لم يتم تحديد شيء", "myChannels": "قنواتي", "whatsapp": "واتساب", "telegram": "تيليغرام", "voice": "صوت", "alexa": "أليكسا", "available": "متاح", "invalid": "غير صالح", "testChannel": "قناة اختبار", "getStarted": "ابد<PERSON>", "metaCloudAPI": "Meta Cloud API", "ngage": "NGAGE", "sms": "رسائل نصية", "virtualReceptionist": "سكرتير افتراضي", "email": "بريد إلكتروني", "rcs": "RCS", "chatbot": "روبوت دردشة", "network": "شبكة", "studio": "استوديو", "allChannels": "جميع القنوات", "filters": {"all": "الكل", "native": "أصلي", "text": "نص", "voice": "صوت"}, "tabs": {"available": "متاح", "myChannels": "قنواتي"}}, "nodes": {"agentTransfer": "نقل الوكيل", "appEnd": "نهاية التطبيق", "appStart": "بدء التطبيق", "choice": "خيار", "choiceOption": "خيار الاختيار", "feedback": "تعليقات", "flowConnector": "موصل التدفق", "http": "HTTP", "interactiveMessage": "رسالة تفاعلية", "language": "اللغة", "message": "رسالة", "notification": "إشعار", "payment": "دفع", "script": "سكربت", "text": "نص", "waitDelay": "انتظار التأخير", "whatsapp": "واتساب"}, "bots": {"testBot": "روبوت اختبار", "testChatbot": "روبوت دردشة اختبار", "aChatbot": "روبوت دردشة اختبار", "aChatbotDescription": "وصف روبوت دردشة اختبار", "myFlow": "تدفقي", "lastUpdatedToday": "آخر تحديث اليوم"}, "whatsapp": {"onboarding": {"ngage": {"description": "دمج WABA باستخدام قناة NGAGE WhatsApp ودمجه مع روبوتك."}, "meta": {"description": "دمج WABA باستخدام Meta Cloud API وربط روبوتك عبر شريك BSP."}}}, "loading": {"hangTight": "انتظر! نحن نجهز مساحة عملك...", "almostThere": "على وشك الانتهاء..."}, "timePicker": {"hour": "ساعة", "min": "دقيقة", "amPm": "صباحًا/مساءً"}, "richTextEditor": {"bold": "غامق", "italic": "مائل", "underline": "مسطر", "strikeThrough": "مشطوب", "highlight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "superscriptSubscript": "مرتفع/منخفض", "emoji": "إيموجي"}, "notification": {"notificationChannel": "اختر قناة الإشعار", "configureMessage": "تكوين الرسالة", "configureSMS": "تكوين الرسائل النصية", "selectSenderID": "اختر معرف المرسل", "recipientMSISDN": "أدخل MSISDN المستلم", "configureEmail": "تكوين البريد الإلكتروني", "selectEmail": "اختر عنوان البريد الإلكتروني", "recipientEmail": "أدخل عنوان البريد الإلكتروني للمستلم", "enterSubjectOfEmail": "أدخل موضوع البريد الإلكتروني"}}