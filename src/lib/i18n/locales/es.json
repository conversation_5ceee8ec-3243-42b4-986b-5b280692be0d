{"common": {"search": "Buscar", "filter": "Filtrar", "create": "CREAR", "save": "GUARDAR", "submit": "Enviar", "cancel": "CANCELAR", "delete": "Eliminar", "add": "AÑADIR", "clone": "Clonar", "export": "Exportar", "edit": "<PERSON><PERSON>", "yes": "SÍ", "no": "NO", "selectOption": "Seleccionar opción", "getStarted": "COMENZAR", "preview": "Vista previa", "publish": "PUBLICAR", "duplicate": "Duplicar", "versionHistory": "Historial de versiones", "flows": "<PERSON><PERSON><PERSON>", "debugger": "Depurador", "message": "Men<PERSON><PERSON>", "image": "Imagen", "file": "Archivo", "video": "Vídeo", "addViaUrl": "Añadir vía URL", "enterFileUrl": "Introducir URL del archivo", "maxSize": "<PERSON><PERSON><PERSON> máximo: {{size}} MB", "clickOrDrag": "Haga clic o arrastre el archivo {{type}} aquí", "clickOrDragFiles": "Haga clic o arrastre un archivo a esta área para subirlo", "writeMessage": "Escribir <PERSON>", "typeMessage": "Escribe tu mensaje...", "fillAboveField": "Rellene el formulario de arriba para continuar", "dateRange": "Seleccione un rango de fechas", "trackOrder": "Rast<PERSON>r mi pedido", "cancelOrder": "Cancelar mi pedido", "chatWithAgent": "Chatear con un agente", "viewSimilarProducts": "Ver productos similares", "hello": "<PERSON><PERSON>, {{name}}!", "howCanIHelp": "¿Cómo puedo ayudarte hoy?", "searchFlows": "Buscar flujos...", "onboarding": "Integración inicial", "notFound": "No encontrado", "enterValidValue": "Por favor ingrese un valor válido", "translateTo": "Traducir a", "translate": "TRADUCIR", "nothingToShow": "Nada que mostrar", "generate": "Generar", "close": "<PERSON><PERSON><PERSON>", "nodeId": "ID del nodo:", "noData": "Sin datos", "searchEllipsis": "Buscar...", "justNow": "<PERSON><PERSON> ahora", "update": "Actualizar", "error": "Error", "somethingWrong": "Algo salió mal", "offlineError": "Actualmente estás desconectado. Por favor, revisa tu conexión a internet.", "saveChanges": "GUARDAR CAMBIOS", "date": "<PERSON><PERSON>", "loading": "Cargando...", "build": "Construir", "import": "Importar", "share": "Compartir", "goBack": "Volver", "noNode": "No se encontró ninguna coincidencia", "versions": "Versiones", "intent": "Intención"}, "chatbot": {"botPublishedSuccessfully": "¡Bot publicado con éxito!", "untitled": "Intitulada", "noDomain": "PREDETERMINADO", "noDescription": "Sin descripción", "confirmDelete": "CONFIRMAR ELIMINACIÓN", "deleteMessage": "¿Seguro que quieres eliminar este chatbot?", "chatbotDeleted": "Chatbot eliminado con éxito", "noCancel": "NO, CANCELAR", "yesDelete": "SÍ, ELIMINAR", "newChatbotPrefix": "Nuevo‑Chatbot", "cloneFailed": "Error al clonar el chatbot.", "buildSuccess": "¡Bot construido con éxito!", "buildFailed": "Error al construir el bot.", "buildStreamError": "Error al conectar con el flujo de construcción.", "defaultTitle": "<PERSON>", "defaultDomain": "E‑com", "defaultDescription": "Ayuda a los clientes a navegar el proceso de compra digital.", "createChatbot": "Nuevo chatbot creado con éxito"}, "debugger": {"logs": "Registros", "aiAnalysis": "Análisis de IA", "sessionData": "Datos de la sesión", "aiAnalysisContent": "Contenido de análisis de IA próximamente.", "sessionDataContent": "Contenido de datos de la sesión próximamente.", "noAiAnalysisLogs": "No hay registros de análisis de IA disponibles.", "noSessionDataLogs": "No hay registros de datos de sesión disponibles.", "noLogs": "No hay registros disponibles."}, "preview": {"confirmDialog": "¿Quieres terminar esta conversación?", "confirmDialogDesc": "Esto borrará el chat y cerrará la ventana"}, "validation": {"maxLength": "Este campo no puede exceder de {{count}} caracteres.", "BLANK_URL": "La URL no puede estar vacía.", "URL_TOO_LONG": "La URL es demasiado larga.", "INVALID_URL": "Formato de URL inválido.", "URL_DISPLAY_TEXT_TOO_LONG": "El texto de visualización no puede exceder de 100 caracteres.", "invalidPhoneNumber": "Número de teléfono inválido para {{field}}.", "fieldRequired": "{{field}} es obligatorio.", "invalidEmail": "Correo electrónico inválido para {{field}}.", "passwordMinLength": "La contraseña para {{field}} debe tener al menos 6 caracteres.", "invalidTimeFormat": "Formato de hora inválido para {{field}}.", "invalidDate": "<PERSON><PERSON> para {{field}}.", "pastDateRequired": "{{field}} debe ser una fecha pasada (antes de {{date}})", "futureDateRequired": "{{field}} debe ser una fecha futura (después de {{date}})", "invalidNumber": "Número in<PERSON> para {{field}}"}, "home": {"title": "NeuraTalk AI", "description": "es una solución de IA conversacional de vanguardia diseñada para mejorar la participación del cliente, automatizar el soporte y agilizar las operaciones comerciales.", "noResults": "noResultados", "lastUpdated": "Última actualización {{date}}"}, "editor": {"chatbotName": "Nombre del Chatbot", "domain": "<PERSON>inio", "description": "Descripción", "uploadImage": "Haga clic o arrastre un archivo a esta área para subirlo", "uploadFormat": "(Tamaño: <PERSON><PERSON> 5MB | Formato: jpg, png)", "unsupportedFile": "Tipo de archivo no soportado", "fileTooLarge": "El archivo debe ser menor a 2MB", "invalidName": "Solo se <PERSON>en letras, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> (‑), gui<PERSON> bajos (_), y puntos (.)", "invalidImageFile": "<PERSON>r <PERSON>, suelta un archivo de imagen válido (png, jpg, jpeg, webp, gif, svg)", "nameRequired": "El nombre del chatbot es obligatorio", "nameMaxError": "El nombre del chatbot no puede exceder 50 caracteres", "domainRequired": "El dominio es obligatorio", "descMaxError": "La descripción no puede exceder 150 caracteres", "updateSuccess": "Chatbot actualizado exitosamente", "descRequired": "La descripción es obligatoria", "updateError": "Error al actualizar el bot", "writeMessage": "Escri<PERSON>"}, "navigation": {"neuraTalk": "NeuraTalk", "create": "<PERSON><PERSON><PERSON>"}, "domains": {"it": "ÉL", "ecomm": "E‑comercial", "telecom": "Telecom", "retail": "Venta al por menor", "travel": "<PERSON><PERSON><PERSON>", "other": "<PERSON><PERSON>"}, "emptyState": {"title": "Nada aquí to<PERSON>", "description": "Actualmente no hay contenido para mostrar."}, "intents": {"title": "Intenciones", "addTitle": "AÑADIR INTENCIÓN", "editTitle": "EDITAR INTENCIÓN", "name": "Nombre de la Intención", "namePlaceholder": "Nombre de la intención", "nameLabel": "Nombre de la intención", "nameRequired": "El nombre de la intención es obligatorio.", "startAdding": "Comienza a añadir intenciones", "noFlowsConnected": "No hay flujos conectados", "selectToManage": "Selecciona una intención para gestionar expresiones", "loading": "Cargando intenciones.", "loadingError": "Error cargando intenciones.", "intentAdded": "Intención añadida con éxito.", "intentUpdated": "Intención actualizada con éxito.", "intentDeleted": "Intención eliminada con éxito.", "confirmDeleteTitle": "CONFIRMAR ELIMINACIÓN DE INTENCIÓN", "deleteConfirmationMessage": "¿Seguro que quieres eliminar esta intención?", "utterances": {"title": "Expresiones", "addTitle": "AÑADIR EXPRESIÓN", "editTitle": "EDITAR EXPRESIÓN", "enterPlaceholder": "Introduce la expresión", "startAdding": "Comienza a añadir expresiones", "emptyError": "La expresión no puede estar vacía.", "loading": "Cargando expresiones.", "loadingError": "Error cargando expresiones.", "utteranceAdded": "Expresión añadida.", "utteranceUpdated": "Expresión actualizada.", "utteranceDeleted": "Expresión eliminada.", "confirmDeleteTitle": "CONFIRMAR ELIMINACIÓN DE EXPRESIÓN", "deleteConfirmationMessage": "¿Seguro que quieres eliminar esta expresión?"}}, "entities": {"title": "Entidades", "addTitle": "AÑADIR ENTIDAD", "entityName": "Nombre de la Entidad", "entityNamePlaceholder": "Nombre de la entidad", "type": "Tipo", "selectType": "Seleccionar tipo", "enablePartialMatch": "Habilitar coincidencia parcial", "startAdding": "Empieza a añadir entidades", "loading": "Cargando entidades...", "error": "Error cargando entidades.", "noEntitiesFound": "No se encontraron entidades", "searchEntities": "Buscar entidades...", "selected": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "removeEntity": "Eliminar entidad", "types": {"text": "Texto", "list": "Lista", "regex": "REGEX"}, "table": {"name": "Nombre", "type": "Tipo", "value": "Valor", "action": "Acción"}, "validation": {"nameRequired": "El nombre de la entidad es obligatorio.", "typeRequired": "El tipo de entidad es obligatorio.", "valueRequired": "El valor es obligatorio."}, "addValue": "<PERSON><PERSON><PERSON> valor", "editTitle": "EDITAR ENTIDAD", "regexValuePlaceholder": "Valor Regex", "entityAdded": "Entidad añadida con éxito.", "entityUpdated": "Entidad actualizada con éxito.", "entityDeleted": "Entidad eliminada con éxito.", "confirmDeleteTitle": "CONFIRMAR ELIMINACIÓN DE ENTIDAD", "deleteConfirmationMessage": "¿Seguro que quieres eliminar esta entidad?"}, "train": {"entities": {"title": "Entidades", "content": "Contenido de Entidades", "addTitle": "AÑADIR ENTIDAD", "nameLabel": "Nombre de la entidad", "intentIdLabel": "ID de intención", "metadataLabel": "Metadatos (JSON)", "metadataPlaceholder": "Introduce metadatos en formato JSON", "loading": "Cargando entidades...", "error": "Error cargando entidades.", "validation": {"nameRequired": "El nombre de la entidad es obligatorio.", "intentIdRequired": "El ID de intención es obligatorio.", "invalidJson": "Formato JSON inválido para los metadatos."}}, "synonyms": {"title": "Sinónimos", "content": "Contenido de Sinónimos"}, "smallTalk": {"title": "Pequeña charla", "content": "Contenido de pequeña charla"}, "trainFromLogs": {"title": "Entrenar desde registros", "content": "Contenido de entrenar desde registros"}, "tabs": {"intentUtterances": "Expresiones de intención", "entities": "Entidades", "faqs": "Preguntas frecuentes", "synonyms": "Sinónimos", "smallTalk": "Pequeña charla", "trainFromLogs": "Entrenar desde registros"}}, "faqs": {"title": "Preguntas y Respuestas", "category": {"title": "Categoría", "addTitle": "AÑADIR CATEGORÍA", "editTitle": "EDITAR CATEGORÍA", "nameLabel": "Nombre de la categoría", "nameRequired": "El nombre de la categoría es obligatorio.", "startAdding": "Comienza a añadir categorías", "selectToManage": "Selecciona una categoría para gestionar preguntas", "categoryAdded": "Categoría añadida con éxito.", "categoryUpdated": "Categoría actualizada con éxito.", "categoryDeleted": "Categoría eliminada con éxito.", "confirmDeleteTitle": "CONFIRMAR ELIMINACIÓN DE CATEGORÍA", "deleteConfirmationMessage": "¿Seguro que quieres eliminar esta categoría?"}, "loading": "Cargando FAQs.", "loadingError": "Error cargando FAQs.", "items": {"loading": "Cargando elementos de FAQ...", "loadingError": "Error cargando elementos de FAQ.", "startAdding": "Comienza a añadir preguntas", "addTitle": "AÑADIR PREGUNTA", "editTitle": "EDITAR PREGUNTA", "questionLabel": "Preguntas", "questionPlaceholder": "Introduce la pregunta", "questionEmpty": "La pregunta no puede estar vacía.", "atLeastOne": "Se requiere al menos una pregunta.", "answerLabel": "Respuesta", "answerPlaceholder": "Introduce la respuesta", "answerEmpty": "La respuesta no puede estar vacía.", "linkFlowLabel": "Vincular flujo", "chooseFlowPlaceholder": "Elige el flujo", "primaryLabel": "Primario", "questionPrefix": "P", "answerPrefix": "R", "questionsAdded": "Preguntas añadidas.", "questionsUpdated": "Preguntas actualizadas.", "maxQuestions": "<PERSON><PERSON><PERSON> a<PERSON> un máximo de {{count}} preguntas.", "questionsDeleted": "Preguntas eliminadas.", "confirmDeleteTitle": "CONFIRMAR ELIMINACIÓN DE FAQ", "deleteConfirmationMessage": "¿Seguro que quieres eliminar este elemento de FAQ?", "limitQuestionError": "Límite alcanzado. Una sección de preguntas frecuentes no puede contener más de 10 preguntas"}, "validation": {"questionRequired": "La pregunta es obligatoria.", "atLeastOneQuestion": "Se requiere al menos una pregunta.", "answerRequired": "La respuesta es obligatoria."}}, "fileUpload": {"fileTooLarge": "El archivo debe ser menor que {{size}} MB y del tipo {{type}}", "someFilesRejected": "Algunos archivos fueron rechazados. Asegúrate de que los tipos sean correctos y el tamaño < {{size}} MB.", "failedToUpload": "Error al subir: {{filename}}"}, "tabs": {"contentComingSoon": "Contenido de la pestaña {{tabName}} próximamente"}, "builder": {"tabs": {"design": "Diseño", "train": "Entrenar", "channels": "Canales", "agentTransfer": "Transferencia de agente", "integrations": "Integraciones", "settings": "Configuraciones"}}, "flows": {"untitledFlow": "flujossintí<PERSON>lo", "noFlows": "No se encontró ninguna coincidencia", "welcome": "Bienvenido", "fallback": "Alternativa", "targetFlow": "Flujo objetivo", "existingFlow": "Recordar contexto entre el flujo de conexión", "errorLoading": "Error cargando flujos", "newFlow": "Nuevo flujo", "fetchError": "Error al obtener flujos", "flowDelete": "Flujo eliminado con éxito", "flowNotCreated": "No se pudo crear el flujo", "flowDeleted": "Flujo eliminado con éxito", "flowNotDeleted": "No se pudo eliminar el flujo", "flowDuplicated": "Flujo duplicado con éxito", "flowNotDuplicated": "No se pudo duplicar el flujo", "flowRenamed": "Flujo renombrado con éxito", "flowNotRenamed": "No se pudo renombrar el flujo", "confirmDelete": "Confirmar <PERSON>", "deleteConfirmationMessage": "¿Estás seguro de que deseas eliminar el flujo **{flowName}**?"}, "agentTransfer": {"transfer": "Integra el agente desde la página ‘Transferir agente’ para configurar el agente nativo", "selectAgentTransfer": "Seleccionar transferencia de agente", "nothingSelected": "<PERSON>da se<PERSON>", "filters": {"all": "Todos", "native": "Nativo", "thirdParty": "Terceros"}, "tabs": {"available": "Disponible", "myAgentTransfers": "Mis transferencias de agente"}, "setupHeading": "Configurar", "setupDescription": "Proporciona los detalles a continuación para activar el soporte de {{agentTransferName}} para el chatbot.", "generateToken": "GENERAR TOKEN", "liveAgentPortalDetails": "Detalles del portal de agente en vivo", "pendingStatus": "Pendiente", "remove": "REMOVER", "chatbotNameLabel": "Nombre del chatbot:", "accessTokenLabel": "Token de acceso:", "shareInstruction": "Comparte lo anterior con el administrador del agente para la activación."}, "settings": {"language": "Idioma", "nlu": "NLU", "personalization": "Personalización", "llmConfiguration": "Configuración LLM", "cannedResponses": "Respuestas predefinidas", "loremDescription": "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor", "languages": "Idiomas", "yourLanguages": "<PERSON><PERSON>", "noLanguagesSelected": "Ningún idioma seleccionado", "availableLanguages": "Idiomas disponibles", "searchLanguages": "Buscar idiomas...", "defaultTag": "Predeterminado", "allSelectedLanguages": "Todos los idiomas seleccionados", "languagesSaved": "Idiomas guardados con éxito", "languageDisabledSuccessfully": "Idioma deshabilitado con éxito", "disableLanguageConfirmationTitle": "Deshabilitar idioma", "disableLanguageConfirmationMessage": "¿Estás seguro de que quieres deshabilitar este idioma del bot?"}, "stencil": {"nodes": "Nodos", "searchNodes": "Buscar nodos...", "engage": "Interactuar", "utilities": "Utilidades", "marketplace": "Marketplace"}, "platform": {"web": "Web", "mobile": "Móvil"}, "pagination": {"previous": "Anterior", "next": "Siguient<PERSON>", "morePages": "<PERSON>ás páginas", "loadingMore": "Cargando más...", "noItemsFound": "No se encontraron elementos", "errorLoadingData": "<PERSON>rror cargando da<PERSON>", "tryAgain": "Intentar de nuevo", "defaultFilterUI": "UI de filtro predeterminada – personalizable con prop children"}, "form": {"loadingForm": "Cargando formulario...", "typing": "Escribiendo...", "enterLabel": "Introducir etiqueta", "prompt": "Ingresar mensaje", "textField": "Campo de texto", "label": "Etiqueta", "required": "Requerido", "lableRequired": "La etiqueta es obligatoria", "promptRequired": "Une invite est requise"}, "errors": {"failedToSend": "Error al enviar mensaje", "unexpectedResponse": "Respuesta inesperada del servidor", "somethingWrong": "Algo salió mal"}, "channels": {"selectWABA": "Selecciona un número WABA para conectar", "changeNumber": "CAMBIAR NÚMERO", "webhook": "Webhook", "webhookInstruction": "Pega este Webhook contra el número WABA en el canal NGAGE WhatsApp para integrar.", "switchToMeta": "Cambiar a Meta Cloud API", "switchDescription": "Cambia a Meta Cloud API y vincula tu Chatbot vía BSP asociado.", "switch": "CAMBIAR", "connect": "CONECTAR", "selectChannels": "Selecciona canales para configurar", "nothingSelected": "<PERSON>da se<PERSON>", "myChannels": "Mis canales", "whatsapp": "WhatsApp", "telegram": "Telegram", "voice": "Voz", "alexa": "Alexa", "available": "Disponible", "invalid": "INVÁLIDO", "testChannel": "Canal de prueba", "getStarted": "COMENZAR", "metaCloudAPI": "Meta Cloud API", "ngage": "NGAGE", "sms": "SMS", "virtualReceptionist": "Recepcionista virtual", "email": "Correo electrónico", "rcs": "RCS", "chatbot": "<PERSON><PERSON><PERSON>", "network": "Red", "studio": "Estudio", "allChannels": "Todos los canales", "filters": {"all": "Todos", "native": "Nativo", "text": "Texto", "voice": "Voz"}, "tabs": {"available": "Disponible", "myChannels": "Mis canales"}}, "nodes": {"agentTransfer": "Transferencia de agente", "appEnd": "Fin de la app", "appStart": "Inicio de <PERSON>", "choice": "Elección", "choiceOption": "Opción de elección", "feedback": "Retroalimentación", "flowConnector": "<PERSON><PERSON> de <PERSON>", "http": "HTTP", "interactiveMessage": "Mensaje interactivo", "language": "Idioma", "message": "Men<PERSON><PERSON>", "notification": "Notificación", "payment": "Pago", "script": "Guion", "text": "Texto", "waitDelay": "<PERSON><PERSON><PERSON> retardo", "whatsapp": "WhatsApp"}, "bots": {"testBot": "<PERSON><PERSON>", "testChatbot": "<PERSON><PERSON><PERSON>", "aChatbot": "Un chatbot de prueba", "aChatbotDescription": "Descripción de un chatbot de prueba", "myFlow": "Mi flujo", "lastUpdatedToday": "Última actualización hoy"}, "whatsapp": {"onboarding": {"ngage": {"description": "Integra WABA usando el canal NGAGE WhatsApp e intégralo con tu Chatbot."}, "meta": {"description": "Integra WABA usando Meta Cloud API y vincula tu Chatbot vía BSP asociado."}}}, "loading": {"hangTight": "¡Aguanta! Estamos configurando tu espacio de trabajo...", "almostThere": "Ya casi llega..."}, "timePicker": {"hour": "<PERSON><PERSON>", "min": "Min", "amPm": "AM/PM"}, "richTextEditor": {"bold": "Negrita", "italic": "Cursiva", "underline": "Subrayado", "strikeThrough": "<PERSON><PERSON><PERSON>", "highlight": "Resaltar", "superscriptSubscript": "Superíndice/Subíndice", "emoji": "<PERSON><PERSON><PERSON>"}, "notification": {"notificationChannel": "Seleccionar canal de notificación", "configureMessage": "<PERSON>fi<PERSON><PERSON> men<PERSON>", "configureSMS": "Configurar SMS", "selectSenderID": "Seleccionar ID del remitente", "recipientMSISDN": "Introduce el MSISDN del destinatario", "configureEmail": "Configurar correo electrónico", "selectEmail": "Seleccionar dirección de correo", "recipientEmail": "Introduce la dirección de correo del destinatario", "enterSubjectOfEmail": "Introduce el asunto del correo electrónico"}}