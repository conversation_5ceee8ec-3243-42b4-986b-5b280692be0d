import { getErrorMessage } from '../errorUtils';
import { vi } from 'vitest';

// Mock the i18n module
vi.mock('../../i18n', () => ({
  i18n: {
    t: vi.fn((key: string) => `${key}`),
  },
}));

describe('getErrorMessage', () => {
  it('should return detailed error message if available', () => {
    const errorResponse = {
      success: false,
      error: {
        code: 'VALIDATION_ERROR',
        message: 'Invalid request body',
        details: [
          {
            validation: 'regex',
            code: 'invalid_string',
            message:
              'Name must start with a letter and contain only letters, numbers, spaces, underscores, and hyphens',
            path: ['name'],
          },
        ],
      },
      timestamp: '2025-09-01T15:44:40.579Z',
    };
    expect(getErrorMessage(errorResponse)).toBe(
      'Name must start with a letter and contain only letters, numbers, spaces, underscores, and hyphens'
    );
  });

  it('should return general error message if details are not available', () => {
    const errorResponse = {
      success: false,
      error: {
        code: 'GENERIC_ERROR',
        message: 'Something went wrong on the server',
      },
      timestamp: '2025-09-01T15:44:40.579Z',
    };
    expect(getErrorMessage(errorResponse)).toBe('Something went wrong on the server');
  });

  it('should return generic fallback message if no error object is present', () => {
    const errorResponse = {
      success: true,
      data: {},
      timestamp: '2025-09-01T15:44:40.579Z',
    };
    expect(getErrorMessage(errorResponse)).toBe('common.somethingWrong');
  });

  it('should return generic fallback message for null or undefined input', () => {
    expect(getErrorMessage(undefined)).toBe('common.somethingWrong');
  });

  it('should handle error object with empty details array', () => {
    const errorResponse = {
      success: false,
      error: {
        code: 'EMPTY_DETAILS',
        message: 'Error with empty details',
        details: [],
      },
      timestamp: '2025-09-01T15:44:40.579Z',
    };
    expect(getErrorMessage(errorResponse)).toBe('Error with empty details');
  });
});
