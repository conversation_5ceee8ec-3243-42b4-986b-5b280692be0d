import { ApiResponse } from '@/types';
import { i18n } from '../i18n';

/**
 * Extracts a user-friendly error message from an API response object.
 * Prioritizes detailed messages if available, otherwise falls back to general error message.
 * @param errorResponse The API response object, which may contain an error structure.
 * @returns A string containing the user-friendly error message.
 */
export const getErrorMessage = (errorResponse?: ApiResponse): string => {
  if (errorResponse?.error) {
    if (errorResponse.error.details && errorResponse.error.details.length > 0) {
      return errorResponse.error.details[0].message;
    } else if (errorResponse.error.message) {
      return errorResponse.error.message;
    }
  }
  return i18n.t('common.somethingWrong'); // Fallback to a generic message
};
