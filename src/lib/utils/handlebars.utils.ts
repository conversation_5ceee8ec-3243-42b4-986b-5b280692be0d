
interface ApiNode {
  name: string;
  type: string;
  description: string;
  children?: ApiNode[];
  valueSchema?: ApiNode[];
}

export const flattenApiData = (data: ApiNode[], prefix: string = ''): string[] => {
  let result: string[] = [];

  data.forEach(node => {
    const currentPath = prefix ? `${prefix}.${node.name}` : node.name;
    result.push(currentPath);

    if (node.children && node.children.length > 0) {
      result = result.concat(flattenApiData(node.children, currentPath));
    }

    if (node.valueSchema && node.valueSchema.length > 0) {
      result = result.concat(flattenApiData(node.valueSchema, currentPath));
    }
  });

  return result;
};
