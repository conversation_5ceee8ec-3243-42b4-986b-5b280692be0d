import React from 'react';
import GenericTabbedModule from '@/modules/common/GenericTabbedModule';
import GenericMainContent from '@/modules/common/GenericMainContent';
import { AgentTransferFilterType, AgentTransferMainTab } from './enums';
import { availableAgentTransfers, filters, mainAgentTransferTab, myAgentTransfers } from './config';
import MainContent from './MainContent';

import { Bot } from '@/types'; // Import Bot type
import { GenericItem } from '../common/types';

interface AgentTransfersTabProps {
  bot: Bot;
}

const AgentTransfersTab: React.FC<AgentTransfersTabProps> = ({ bot }) => {
  const renderAgentTransferContent = (item: GenericItem) => {
    const selectedAgentTransfer = availableAgentTransfers.find(transfer => transfer.id === item.id);

    if (!selectedAgentTransfer) {
      return (
        <GenericMainContent
          selectedItem={null}
          renderContent={() => null}
          emptyStateTitleKey="agentTransfer.selectAgentTransfer"
          emptyStateDescriptionKey="agentTransfer.nothingSelected"
        />
      );
    }

    return (
      <GenericMainContent
        selectedItem={selectedAgentTransfer}
        renderContent={() => (
          <MainContent selectedAgentTransfer={selectedAgentTransfer} bot={bot} />
        )}
        emptyStateTitleKey="agentTransfer.selectAgentTransfer"
        emptyStateDescriptionKey="agentTransfer.nothingSelected"
      />
    );
  };

  return (
    <GenericTabbedModule
      mainTabEnum={AgentTransferMainTab}
      filterTypeEnum={AgentTransferFilterType}
      availableItems={availableAgentTransfers}
      myItems={myAgentTransfers}
      filters={filters}
      mainTabsConfig={mainAgentTransferTab}
      emptyStateTitleKey="agentTransfer.selectAgentTransfer"
      emptyStateDescriptionKey="agentTransfer.nothingSelected"
      renderMainContent={renderAgentTransferContent}
      hideTabNavigation={true}
    />
  );
};

export default AgentTransfersTab;
