import React from 'react';
import { Card, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { Upload, Copy, TvMinimalPlay } from 'lucide-react';
import { WebChannelConfig } from '../types';
import { useBotIdParam } from '@/hooks/useRouterParam';

interface DeployContentProps {
  config: WebChannelConfig;
}

const DeployContent: React.FC<DeployContentProps> = ({ config }) => {
  const { t } = useTranslation();
  const { botId } = useBotIdParam();

  const embedCode = `<!-- Chatbot Widget Embed Code -->

<script src="https://ik.imagekit.io/kggne4pv7or/chatbot-sdk_qnQk42MHV.js"></script>
<script>
  // Initialize your chatbot here
  ChatbotSDK.init({
    botId: '${botId}',
    botName: '${config.botName}',
    botDescription: '${config.botDescription}',
    primaryColor: '${config.primaryColor}',
    secondaryColor: '${config.secondaryColor}',
    tertiaryColor: '${config.tertiaryColor}',
    fontFamily: '${config.fontFamily}',
    fontSize: '${config.fontSize}',
    botAvatarUrl: '${config.botAvatarUrl}',
    // Add other configurations as needed
  });
</script>
<!-- End Chatbot Widget Embed Code -->`;

  const handleCopyCode = () => {
    navigator.clipboard.writeText(embedCode);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-base">{t('channels.launchTheBot')}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex space-x-2 mb-6">
          <Button variant="solid" className="px-6 py-2">
            {t('channels.web')}
          </Button>
          <Button variant="outline" className="px-6 py-2">
            Mobile SDK
          </Button>
        </div>

        <p className="text-sm text-muted-foreground mb-4">{t('channels.copyCodeInstructions')}</p>

        <div className="relative bg-tertiary-800 text-foreground p-4 rounded-md font-mono text-sm overflow-auto mb-6">
          <Button
            variant="ghost"
            size="icon"
            className="absolute top-2 right-2 text-tertiary-400 hover:text-tertiary-50"
            onClick={handleCopyCode}
          >
            <Copy className="h-4 w-4" />
          </Button>
          <pre className="text-white">
            <code>{embedCode}</code>
          </pre>
        </div>

        <div className="flex flex-col sm:flex-row gap-4">
          <Button
            variant="ghost"
            className="!bg-tertiary-100 border border-tertiary-300 rounded-2xl w-44 whitespace-normal break-words"
          >
            <TvMinimalPlay className="h-5 w-5 mr-2" />
            {t('channels.experienceOnWebsite')}
          </Button>
          <Button variant="ghost" className="rounded-2xl w-44 whitespace-normal break-words">
            <Upload className="h-5 w-5 mr-2" />
            {t('channels.copyLinkToShareBot')}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default DeployContent;
