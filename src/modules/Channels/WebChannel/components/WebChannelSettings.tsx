import React from 'react';
import { Tabs, TabsContent } from '@/components/ui/tabs';
import { useTranslation } from 'react-i18next';

import WidgetSettingsContent from './WidgetSettingsContent';
import DeployContent from './DeployContent';
import { WebChannelConfig } from '../types';
import GenericTabNavigation from '@/modules/common/GenericTabNavigation';
import { webChannelConfigurationTab } from '../config';
import { useTabPersistence } from '@/hooks/useTabPersistence';

interface WebChannelSettingsProps {
  config: WebChannelConfig;
  onConfigChange: (field: keyof WebChannelConfig, value: string) => void;
}

const WebChannelSettings: React.FC<WebChannelSettingsProps> = ({ config, onConfigChange }) => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useTabPersistence({
    defaultTab: 'widget-settings',
    queryParamName: 'webChannelTab',
  });

  return (
    <div className="flex-1 w-0 max-w-4xl px-6 py-3 overflow-y-auto h-full">
      <h2 className="text-lg font-medium mb-4">{t('channels.chatWidget')}</h2>
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <GenericTabNavigation
          activeTab={activeTab}
          tabs={webChannelConfigurationTab}
          onTabChange={setActiveTab}
        />

        <TabsContent value="widget-settings" className="mt-4">
          <WidgetSettingsContent config={config} onConfigChange={onConfigChange} />
        </TabsContent>
        <TabsContent value="deploy" className="mt-4">
          <DeployContent config={config} />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default WebChannelSettings;
