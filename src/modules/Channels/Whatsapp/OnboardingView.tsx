import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '../../../components/ui/card';
import { OnboardingOption } from './types';
import { OnboardingOptionId } from './config';
import { useTranslation } from 'react-i18next';
import { cn } from '@/lib/utils';

interface OnboardingViewProps {
  options: OnboardingOption[];
  onOptionSelect: (optionId: OnboardingOptionId) => void;
}

const OnboardingView: React.FC<OnboardingViewProps> = ({ options, onOptionSelect }) => {
  const { t } = useTranslation();
  return (
    <div className="flex-1 p-8 max-w-4xl">
      <div className="max-w-3xl">
        <h2 className="text-lg font-medium mb-8">{t('common.onboarding')}</h2>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
          {options.map(option => (
            <Card
              key={option.id}
              className={cn(
                'border border-tertiary-200 transition-shadow',
                !option.disabled && 'hover:shadow-md'
              )}
            >
              <CardContent className="px-8 py-12 text-center">
                <div
                  className={cn(
                    'w-16 h-16 mx-auto mb-6 rounded-full flex items-center justify-center',
                    'bg-tertiary-100'
                  )}
                >
                  <img
                    src={option.icon}
                    alt={option.title}
                    className="w-8 h-8"
                    onError={e => {
                      e.currentTarget.style.display = 'none';
                    }}
                  />
                </div>

                <h3 className="font-semibold mb-2">{option.title}</h3>

                <p className="text-sm text-tertiary-600 mb-6 leading-relaxed">
                  {t(option.descriptionKey)}
                </p>

                <Button
                  onClick={() => onOptionSelect(option.id)}
                  disabled={option.disabled}
                  className={cn(
                    'mt-5 px-6 py-2 rounded-md font-medium',
                    option.disabled
                      ? 'cursor-not-allowed'
                      : 'bg-primary-600 hover:bg-primary-700 text-white'
                  )}
                >
                  {t(option.buttonTextKey)}
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
};

export default OnboardingView;
