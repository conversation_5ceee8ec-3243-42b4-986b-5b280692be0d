import { useState, useEffect, useCallback } from 'react';
import { skipToken } from '@reduxjs/toolkit/query/react';
import { useTranslation } from 'react-i18next';
import WABASelection from './WABASelection';
import OnboardingView from './OnboardingView';
import ConfigurationView from './ConfigurationView';
import { OnboardingOptionId, onboardingOptions, WhatsappViewTab } from './config';
import {
  useGetWhatsappProfilesQuery,
  useConnectWABANumberToBotMutation,
} from '@/store/api/whatsappApi';
import { useGetBotChannelDetailsQuery } from '@/store/api/channelsApi';
import { WhatsappProfile } from '@/types';
import { WABANumber } from './types';
import { useBotIdParam } from '@/hooks/useRouterParam';
import { useToast } from '@/hooks/use-toast';
import SuccessToastMessage from '@/components/SuccessToastMessage';

interface WhatsappViewProps {
  channelId: string;
}

function WhatsappView({ channelId }: WhatsappViewProps) {
  const [view, setView] = useState(WhatsappViewTab.ONBOARDING);
  const [selectedWABANumber, setSelectedWABANumber] = useState<string | null>(null);
  const [onboardingPlatform, setOnboardingPlatform] = useState<OnboardingOptionId | null>(null);
  const [wabaNumbers, setWabaNumbers] = useState<WABANumber[]>([]);
  const [whatsappProfilesState, setWhatsappProfilesState] = useState<WhatsappProfile[]>([]);
  const [configuredWebhookUrl, setConfiguredWebhookUrl] = useState('');
  const [configuredWabaNumber, setConfiguredWabaNumber] = useState('');
  const [isWebhookUrlCopied, setIsWebhookUrlCopied] = useState(false);

  const { botId } = useBotIdParam();
  const { toast } = useToast();
  const { t } = useTranslation();

  useEffect(() => {
    setView(WhatsappViewTab.ONBOARDING);
    setOnboardingPlatform(null);
    setSelectedWABANumber(null);
    setConfiguredWebhookUrl('');
    setConfiguredWabaNumber('');
  }, [channelId]);

  // Fetch bot channel details
  const shouldFetchBotDetails = !!botId;
  const { data: botChannelDetails, isLoading: isBotChannelDetailsLoading } =
    useGetBotChannelDetailsQuery(
      shouldFetchBotDetails ? { botId: botId || '', channelId } : skipToken,
      { refetchOnMountOrArgChange: true }
    );
  // Fetch WhatsApp profiles
  const shouldFetchProfiles = true;
  const {
    data: whatsappProfilesData,
    isLoading: isWhatsappProfilesLoading,
    refetch: refetchWhatsappProfiles,
  } = useGetWhatsappProfilesQuery(shouldFetchProfiles ? {} : skipToken, {
    refetchOnMountOrArgChange: true,
  });
  const [connectWABANumberToBot] = useConnectWABANumberToBotMutation();

  // Handle profiles response
  useEffect(() => {
    if (whatsappProfilesData?.data?.items?.length) {
      setWhatsappProfilesState(whatsappProfilesData?.data?.items || []);
      setWabaNumbers(
        whatsappProfilesData?.data?.items.map(profile => ({
          id: profile.phone_number_id,
          number: profile.wa_number,
        }))
      );
    } else {
      setWhatsappProfilesState([]);
      setWabaNumbers([]);
    }
  }, [whatsappProfilesData]);

  // Handle bot channel details
  useEffect(() => {
    if (!isBotChannelDetailsLoading && botChannelDetails?.data) {
      setView(WhatsappViewTab.CONFIGURATION);
      setConfiguredWebhookUrl(botChannelDetails.data.botChannelConfig?.webhookUrl || '');
      setConfiguredWabaNumber(botChannelDetails.data.botChannelConfig?.wabaNumber || '');
      setOnboardingPlatform(botChannelDetails.data.botChannelConfig?.onboardingPlatform || null);
    } else if (!isBotChannelDetailsLoading && !botChannelDetails?.data) {
      setView(WhatsappViewTab.ONBOARDING);
    }
  }, [isBotChannelDetailsLoading, botChannelDetails]);

  const onOptionSelect = (optionId: OnboardingOptionId) => {
    if (optionId === OnboardingOptionId.Ngage) {
      setOnboardingPlatform(OnboardingOptionId.Ngage);
      setView(WhatsappViewTab.WABA_SELECTION);
    } else if (optionId === OnboardingOptionId.Meta) {
      setOnboardingPlatform(OnboardingOptionId.Meta);
    }
  };

  const onNumberSelect = (number: string) => setSelectedWABANumber(number);
  const onCancel = () => {
    setView(WhatsappViewTab.ONBOARDING);
    setOnboardingPlatform(null);
  };

  const onConnect = async () => {
    if (!selectedWABANumber || !botId) {
      toast({
        title: 'Error',
        description: 'Please select a WABA number and ensure botId is available.',
        variant: 'destructive',
      });
      return;
    }

    const selectedProfile = whatsappProfilesState.find(
      profile => profile.phone_number_id === selectedWABANumber
    );

    if (!selectedProfile) {
      toast({
        title: 'Error',
        description: 'Selected WABA number profile not found.',
        variant: 'destructive',
      });
      return;
    }

    try {
      const response = await connectWABANumberToBot({
        botId,
        channelId,
        wabaPhoneNumberId: selectedWABANumber,
        wabaNumber: selectedProfile.wa_number,
        onboardingPlatform,
      }).unwrap();

      setConfiguredWebhookUrl(response.data?.webhookUrl || '');
      setConfiguredWabaNumber(response.data?.wa_number || '');

      toast({ title: 'Success', description: 'WABA number connected successfully!' });
      setView(WhatsappViewTab.CONFIGURATION);
    } catch {
      toast({
        title: 'Error',
        description: 'Failed to connect WABA number to bot.',
        variant: 'destructive',
      });
    }
  };

  const onChangeNumber = () => {
    setSelectedWABANumber(null);
    setView(WhatsappViewTab.WABA_SELECTION);
    if (shouldFetchProfiles) {
      refetchWhatsappProfiles();
    }
  };

  const onSwitch = () => {
    setSelectedWABANumber(null);
    setView(WhatsappViewTab.WABA_SELECTION);
    setConfiguredWebhookUrl('');
    setConfiguredWabaNumber('');
  };

  const handleCopyWebhookUrl = useCallback(async () => {
    try {
      await navigator.clipboard.writeText(configuredWebhookUrl);
      setIsWebhookUrlCopied(true);
      toast({
        title: <SuccessToastMessage message={'Webhook URL copied to clipboard.'} />,
      });
      setTimeout(() => setIsWebhookUrlCopied(false), 2000);
    } catch (err) {
      toast({ title: 'Error', description: 'Failed to copy webhook URL.', variant: 'destructive' });
    }
  }, [configuredWebhookUrl, toast]);

  const renderView = {
    [WhatsappViewTab.ONBOARDING]: (
      <OnboardingView options={onboardingOptions} onOptionSelect={onOptionSelect} />
    ),
    [WhatsappViewTab.WABA_SELECTION]: (
      <WABASelection
        wabaNumbers={wabaNumbers}
        selectedNumber={selectedWABANumber}
        onNumberSelect={onNumberSelect}
        onCancel={onCancel}
        onConnect={onConnect}
        connectedWabaNumber={configuredWabaNumber}
      />
    ),
    [WhatsappViewTab.CONFIGURATION]: (
      <ConfigurationView
        selectedWABANumber={
          whatsappProfilesState.find(profile => profile.phone_number_id === selectedWABANumber)
            ?.name ?? ''
        }
        webhookUrl={configuredWebhookUrl}
        wabaNumber={configuredWabaNumber}
        onChangeNumber={onChangeNumber}
        onSwitch={onSwitch}
        disableSwitch={true}
        onCopyWebhookUrl={handleCopyWebhookUrl}
        isWebhookUrlCopied={isWebhookUrlCopied}
      />
    ),
  };

  if (isBotChannelDetailsLoading || isWhatsappProfilesLoading) {
    return <p>{t('common.loading')}</p>;
  }

  return renderView[view];
}

export default WhatsappView;
