import React, { useState } from 'react';
import useDebounce from '@/hooks/useDebounce';
import { useBotIdParam } from '@/hooks/useRouterParam';
import GenericTabbedModule from '@/modules/common/GenericTabbedModule.tsx';
import { FilterType, ChannelId, ChannelMainTab } from './enums';
import { filters, mainChannelTab } from './config';
import MainContent from './MainContent'; // Keep Channels' specific MainContent for now
import { Channel } from '@/types';
import { useGetChannelsQuery, useGetAllBotChannelsQuery } from '@/store/api/channelsApi';
import { GenericItem } from '@/modules/common/types'; // Corrected import for GenericItem
import WhatsAppIcon from '@/assets/icons/WhatsApp.png';
import WebhookIcon from '@/assets/icons/webhook.png';
import FacebookIcon from '@/assets/icons/facebook-messenger.png';
import InstagramIcon from '@/assets/icons/Instagram.svg';
import TelegramIcon from '@/assets/icons/telegram.svg';
import AmazonAlexaIcon from '@/assets/icons/Amazon_Alexa_blue_logo.png';
import WebMobileBotIcon from '@/assets/icons/WebMobileBot.svg';
import { skipToken } from '@reduxjs/toolkit/query';

const iconMap: { [key: string]: string } = {
  'web.png': WebMobileBotIcon,
  'whatsapp.png': WhatsAppIcon,
  'webhook.png': WebhookIcon,
  'facebook.png': FacebookIcon,
  'instagram.png': InstagramIcon,
  'telegram.png': TelegramIcon,
  'amazon_alexa.png': AmazonAlexaIcon,
};

const ChannelsTab: React.FC = () => {
  const { botId } = useBotIdParam();
  const [activeMainTab, setActiveMainTab] = useState(String(ChannelMainTab.AVAILABLE));
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedFilter, setSelectedFilter] = useState<string | null>(FilterType.ALL);
  const debouncedSearchTerm = useDebounce(searchTerm, 300);

  const handleFilterChange = (filter: string | null) => {
    setSelectedFilter(filter);
  };
  
  const { data: availableChannelsData } = useGetChannelsQuery(
    {
      search: debouncedSearchTerm,
    },
    { skip: activeMainTab !== ChannelMainTab.AVAILABLE, refetchOnMountOrArgChange: true }
  );

  const { data: myChannelsData } = useGetAllBotChannelsQuery(
    activeMainTab === ChannelMainTab.MY_CHANNELS && botId
      ? { botId, search: debouncedSearchTerm }
      : skipToken,
    { refetchOnMountOrArgChange: true }
  );
  const fetchedAvailableChannels: Channel[] = availableChannelsData?.data?.items ?? [];
  const fetchedMyChannels: GenericItem[] = myChannelsData?.data ?? [];

  const handleMainTabChange = (tab = String(ChannelMainTab.AVAILABLE)) => {
    setActiveMainTab(tab);
  };

  const availableChannels: GenericItem[] = fetchedAvailableChannels.map(channel => ({
    id: channel.id,
    name: channel.name,
    type: channel.channelType,
    channelType: channel.channelType,
    channelKey: channel.channelKey,
    status: channel.status ? 'active' : 'inactive',
    icon: iconMap[channel.logo ?? ''] ?? '/assets/icons/default.png',
  }));

  const myBotChannels: GenericItem[] = fetchedMyChannels.map((channel: GenericItem) => ({
    id: channel.channelId ?? '',
    name: channel.channelName ?? '',
    type: channel.channelType ?? '',
    channelType: channel.channelType,
    channelKey: channel.channelKey,
    status: channel.botChannelStatus ? 'active' : 'inactive',
    icon: iconMap[channel.logo ?? ''] ?? '/assets/icons/default.png',
  }));

  const renderChannelContent = (selectedChannel: GenericItem) => {
    return (
      <MainContent
        selectedChannel={selectedChannel.channelKey as ChannelId}
        channelId={selectedChannel.id}
      />
    );
  };
  return (
    <GenericTabbedModule
      mainTabEnum={ChannelMainTab}
      filterTypeEnum={FilterType}
      availableItems={availableChannels}
      myItems={myBotChannels}
      filters={filters}
      activeFilter={selectedFilter}
      onFilterChange={handleFilterChange}
      mainTabsConfig={mainChannelTab}
      emptyStateTitleKey="channels.selectChannels"
      emptyStateDescriptionKey="channels.nothingSelected"
      renderMainContent={renderChannelContent}
      onMainTabChange={handleMainTabChange}
      searchTerm={searchTerm}
      onSearch={setSearchTerm}
      queryParamName="channelId"
      activeMainTab={activeMainTab}
    />
  );
};

export default ChannelsTab;
