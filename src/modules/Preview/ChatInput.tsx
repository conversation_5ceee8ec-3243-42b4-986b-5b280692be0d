'use client';

import React, { memo } from 'react';
import { Smile, Send } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { Form, FormControl, FormField, FormItem } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { UseFormReturn } from 'react-hook-form';
import { ChatMessage } from './types/types';
import { Button } from '@/components/ui/button';

interface ChatInputProps {
  activeForm: ChatMessage | null;
  loading: boolean;
  isFormValid: () => boolean;
  isSingleTextField: boolean;
  onSubmit: (fields: { message: string }) => void;
  form: UseFormReturn<any>;
  disabled?: boolean;
}

const ChatInput: React.FC<ChatInputProps> = memo(
  ({ activeForm, loading, isSingleTextField, isFormValid, disabled, onSubmit, form }) => {
    const { t } = useTranslation();

    const isButtonDisabled = loading || !!(activeForm && !isFormValid());
    return (
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="p-2 border-t" autoComplete="off">
          <div className="flex flex-row gap-1 items-center rounded-lg border px-2 border-secondary-300 py-1">
            <FormField
              control={form.control}
              name="message"
              render={({ field }) => (
                <FormItem className="flex-1">
                  <FormControl>
                    <Input
                      type="text"
                      placeholder={
                        activeForm ? t('common.fillAboveField') : t('common.typeMessage')
                      }
                      className="border-none px-0 w-60 text-sm bg-transparent focus:outline-none"
                      {...field}
                      disabled={disabled || loading || (!isSingleTextField && disabled)}
                      aria-label="Type your message"
                      autoFocus
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <Button
              type="button"
              variant="ghost"
              className="w-9 h-9 p-0"
              tabIndex={-1}
              disabled={disabled || isButtonDisabled}
            >
              <Smile className="!w-6 !h-6" />
            </Button>

            <Button type="submit" className="w-8 h-8" disabled={isButtonDisabled} aria-label="Send">
              <Send className="w-8 h-8" />
            </Button>
          </div>
        </form>
      </Form>
    );
  }
);

export default ChatInput;
