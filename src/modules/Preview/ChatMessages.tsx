'use client';

import React, { memo, useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { ChatMessage, BotFormFields } from './types/types';
import { RendererType } from './types/enums';
import { getRenderer } from './utils/rendererRegistry';

interface ChatMessagesProps {
  messages: ChatMessage[];
  lastFormPrompt: ChatMessage | null;
  formLocked: boolean;
  onFormSubmit: (fields: BotFormFields) => Promise<void>;
  lastFormFieldValues?: BotFormFields;
  loading?: boolean;
  onFormActiveChange?: (active: boolean) => void;
}

const ChatMessages: React.FC<ChatMessagesProps> = memo(
  ({
    messages,
    lastFormPrompt,
    formLocked,
    onFormSubmit,
    lastFormFieldValues,
    loading,
    onFormActiveChange,
  }) => {
    const chatEndRef = useRef<HTMLDivElement>(null);
    const { t } = useTranslation();
    const hasActiveForm = messages.some(msg => {
      if (msg.nodeType !== RendererType.FORM || !msg.data?.prompt?.length) return false;

      const promptFields = msg.data.prompt;
      const singleField = promptFields.length === 1 ? promptFields[0] : null;

      if (singleField?.fieldType === 'text') return false;

      return !msg.data?.submittedValues;
    });

    // ✅ Notify parent when active form state changes
    useEffect(() => {
      onFormActiveChange?.(hasActiveForm);
    }, [hasActiveForm, onFormActiveChange]);

    return (
      <div className="flex-1 border-none p-4 space-y-3 overflow-y-auto">
        {messages.map((msg, idx) => {
          const RendererComponent = getRenderer(msg.nodeType);
          return RendererComponent ? (
            <RendererComponent
              key={`msg-${idx}`}
              msg={msg}
              idx={idx}
              lastFormPrompt={lastFormPrompt}
              formLocked={formLocked}
              onFormSubmit={onFormSubmit}
              lastFormFieldValues={lastFormFieldValues}
            />
          ) : null;
        })}

        {loading && (
          <div className="flex justify-start">
            <div className="px-4 py-2 rounded-2xl bg-tertiary-100 text-secondary-400 text-base animate-pulse">
              {t('form.typing')}
            </div>
          </div>
        )}

        <div ref={chatEndRef} />
      </div>
    );
  }
);

export default ChatMessages;
