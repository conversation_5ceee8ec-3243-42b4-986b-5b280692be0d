import { useForm } from 'react-hook-form';
import { Button } from '@/components/ui/button';
import { Form, FormField, FormItem, FormMessage, FormControl } from '@/components/ui/form';
import { BotFormFields, FeedbackData } from '../types/types';
import { useTranslation } from 'react-i18next';
import { CommonRendererProps } from '../utils/rendererRegistry';
import FeedbackTypeRenderer from './FeedbackTypeRenderer';

const FeedbackRenderer: React.FC<CommonRendererProps> = ({
  msg,
  idx,
  onFormSubmit = () => Promise.resolve(),
  formLocked = false,
  lastFormPrompt,
}) => {
  const feedbackData = msg.data as FeedbackData;
  const feedbackType = feedbackData.type ?? 'Star';
  const feedbackPrompt = feedbackData.text ?? 'Please provide your feedback:';
  const { t } = useTranslation();
  const form = useForm<{ feedback: string }>({
    defaultValues: { feedback: '' },
  });
  const isLocked = !!feedbackData.submittedValues || (lastFormPrompt !== msg && formLocked);
  const handleSubmit = form.handleSubmit(async values => {
    const data: BotFormFields = {
      feedback: values.feedback,
      feedbackType,
    };
    await onFormSubmit(data);
  });

  return (
    <Form {...form}>
      <form
        key={idx}
        onSubmit={handleSubmit}
        className="p-4 rounded-xl bg-card w-full max-w-md shadow space-y-4"
      >
        <p className="text-sm font-medium">{feedbackPrompt}</p>

        <FormField
          control={form.control}
          name="feedback"
          rules={{ required: 'Feedback is required' }}
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <FeedbackTypeRenderer
                  feedbackType={feedbackType}
                  fieldValue={field.value}
                  onFieldChange={field.onChange}
                  isLocked={isLocked}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <Button
          type="submit"
          disabled={!form.watch('feedback') || isLocked}
          className="w-full mt-4"
        >
          {t('common.submit')}
        </Button>
      </form>
    </Form>
  );
};

export default FeedbackRenderer;
