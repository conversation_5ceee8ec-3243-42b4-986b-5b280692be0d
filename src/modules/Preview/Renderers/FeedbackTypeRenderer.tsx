import React from 'react';
import StarRating from './StarRating';
import ThumbsFeedback from './ThumbsFeedback';
import TextRating from './TextRating';

interface FeedbackTypeRendererProps {
  feedbackType: string;
  fieldValue: string;
  onFieldChange: (value: string) => void;
  isLocked: boolean;
}

const FeedbackTypeRenderer: React.FC<FeedbackTypeRendererProps> = ({
  feedbackType,
  fieldValue,
  onFieldChange,
  isLocked,
}) => {
  switch (feedbackType) {
    case 'Star':
      return (
        <StarRating
          fieldValue={fieldValue}
          onFieldChange={onFieldChange}
          isLocked={isLocked}
        />
      );
    case 'Thumbs':
      return (
        <ThumbsFeedback
          fieldValue={fieldValue}
          onFieldChange={onFieldChange}
          isLocked={isLocked}
        />
      );
    case 'Text':
      return (
        <TextRating
          fieldValue={fieldValue}
          onFieldChange={onFieldChange}
          isLocked={isLocked}
        />
      );
    default:
      return null;
  }
};

export default FeedbackTypeRenderer;