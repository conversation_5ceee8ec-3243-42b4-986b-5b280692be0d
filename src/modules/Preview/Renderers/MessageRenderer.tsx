'use client';

import React from 'react';
import { cn } from '@/lib/utils';
import { RichTextEditor } from '../../rich-text-editor';
import { SenderType, RendererType } from '../types/enums';
import { CommonRendererProps } from '../utils/rendererRegistry';
import { BotMessage, UserMessage } from '../types';
import { User } from 'lucide-react';
import Bot from '@/assets/icons/bot.svg';

const MessageRenderer: React.FC<CommonRendererProps> = ({ msg, idx }) => {
  msg = msg as BotMessage | UserMessage;
  const { sender, nodeType, data: messageData } = msg;
  const isCurrentUser = sender === SenderType.USER;
  const content =
    nodeType === RendererType.USER_MESSAGE
      ? messageData.text
      : (messageData.text ?? messageData.content);
  if (!content) return null;
  return (
    <div key={idx} className={cn('flex gap-2', isCurrentUser ? 'justify-end' : 'justify-start')}>
      {!isCurrentUser && (
        <div className="w-10 h-10 rounded-full overflow-hidden self-end border border-tertiary-200 flex items-center justify-center">
          <img src={Bot} alt="Bot Avatar" className="w-full h-full object-cover" />
        </div>
      )}
      <div
        className={cn(
          'max-w-[80%] px-4 py-2 rounded-2xl shadow-sm text-base',
          sender === SenderType.USER
            ? 'bg-primary-100 text-primary-900 rounded-br-md'
            : 'bg-tertiary-100 text-secondary-900 rounded-bl-md'
        )}
      >
        <RichTextEditor
          content={content}
          readOnly
          isToolbar={false}
          className="bg-transparent border-none shadow-none p-0"
        />
      </div>

      {isCurrentUser && (
        <div className="w-10 h-10 rounded-full overflow-hidden bg-tertiary-100 border border-tertiary-200 flex items-center justify-center self-end">
          <User />
        </div>
      )}
    </div>
  );
};

export default MessageRenderer;
