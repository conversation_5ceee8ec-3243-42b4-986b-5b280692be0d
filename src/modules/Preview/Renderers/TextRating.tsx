import React from 'react';
import { cn } from '@/lib/utils';

interface TextRatingProps {
  fieldValue: string;
  onFieldChange: (value: string) => void;
  isLocked: boolean;
}

const TextRating: React.FC<TextRatingProps> = ({
  fieldValue,
  onFieldChange,
  isLocked,
}) => {
  return (
    <div className="flex gap-2">
      {Array.from({ length: 5 }, (_, i) => {
        const num = (i + 1).toString();
        const isSelected = fieldValue === num;
        return (
          <button
            key={num}
            type="button"
            onClick={() => onFieldChange(num)}
            className={cn(
              'px-3 py-1 border rounded-lg text-sm font-medium transition-colors',
              isSelected
                ? 'bg-primary text-primary-foreground border-primary'
                : 'bg-background text-muted-foreground border-input',
              isLocked ? 'cursor-not-allowed opacity-50' : 'cursor-pointer',
            )}
            disabled={isLocked}
          >
            {num}
          </button>
        );
      })}
    </div>
  );
};

export default TextRating;
