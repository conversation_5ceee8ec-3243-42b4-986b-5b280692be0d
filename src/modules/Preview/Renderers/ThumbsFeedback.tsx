import React from 'react';
import { ThumbsUp, ThumbsDown } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ThumbsFeedbackProps {
  fieldValue: string;
  onFieldChange: (value: string) => void;
  isLocked: boolean;
}

const ThumbsFeedback: React.FC<ThumbsFeedbackProps> = ({
  fieldValue,
  onFieldChange,
  isLocked,
}) => {
  return (
    <div className="flex gap-1">
      <ThumbsUp
        className={cn(
          'w-7 h-7 transition-colors',
          isLocked ? 'cursor-not-allowed opacity-50' : 'cursor-pointer',
          fieldValue === '1' ? 'text-success-500' : 'text-muted-foreground'
        )}
        onClick={!isLocked ? () => onFieldChange('1') : undefined}
        fill={fieldValue === '1' ? 'currentColor' : 'none'}
      />
      <ThumbsDown
        className={cn(
          'w-7 h-7 transition-colors',
          isLocked ? 'cursor-not-allowed opacity-50' : 'cursor-pointer',
          fieldValue === '0' ? 'text-error-500' : 'text-muted-foreground'
        )}
        onClick={!isLocked ? () => onFieldChange('0') : undefined}
        fill={fieldValue === '0' ? 'currentColor' : 'none'}
      />
    </div>
  );
};

export default ThumbsFeedback;
