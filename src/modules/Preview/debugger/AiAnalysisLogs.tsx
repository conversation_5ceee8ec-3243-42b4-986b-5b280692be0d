import React from 'react';
import { useTranslation } from 'react-i18next';
import { DebuggerEvent, DebuggerEventType } from '@/types/botInteraction.type';

interface AiAnalysisProps {
  logs: DebuggerEvent[];
}

const AiAnalysisLogs: React.FC<AiAnalysisProps> = ({ logs }) => {
  const { t } = useTranslation();

  const nluLogs = logs.filter(log => log.type === DebuggerEventType.NLU_LOG);

  return (
    <div className="flex-1 flex flex-col gap-2">
      {nluLogs.length === 0 ? (
        <div className="flex items-center justify-center p-6 bg-muted h-full text-secondary-500">
          <p>{t('debugger.noAiAnalysisLogs')}</p>
        </div>
      ) : (
        nluLogs.map(log => (
          <div
            key={`ai-log-${log.timestamp}`}
            className="flex items-start space-x-3 px-4 py-3 border-b border-secondary-100 bg-secondary-50"
          >
            <div className="flex-1 min-w-0">
              <pre className="text-sm font-mono text-primary-700 overflow-auto">
                {JSON.stringify(log.payload, null, 2)}
              </pre>
            </div>
          </div>
        ))
      )}
    </div>
  );
};

export default AiAnalysisLogs;
