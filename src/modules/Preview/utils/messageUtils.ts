import { format } from 'date-fns';
import {
  BotFormFields,
  ChatMessage,
  FormMessage,
  SenderType,
  RendererType,
  MessageType,
  USER_ID,
  CHANNEL,
} from '../types';

export interface MessagePayload {
  content: string;
  messageType: MessageType;
  botId: string;
  metadata: {
    userId: string;
    channel: string;
    conversationId: string;
    formId?: string;
  };
  formData?: BotFormFields;
}

/**
 * Creates a user message for the chat
 */
export const createUserMessage = (text: string): ChatMessage => ({
  sender: SenderType.USER,
  nodeType: RendererType.USER_MESSAGE,
  data: { text },
});

/**
 * Structures form data for server submission
 */
export const createFormSubmissionPayload = (
  fields: BotFormFields,
  botId: string,
  conversationId: string,
  formId?: string
): MessagePayload => {
  const formattedData = Object.fromEntries(
    Object.entries(fields).map(([key, value]) => {
      if (typeof value === 'string') return [key, value];
      if (value instanceof Date) return [key, format(value, 'yyyy-MM-dd')];
      return [key, value ? String(value) : ''];
    })
  );

  return {
    content: JSON.stringify(formattedData),
    messageType: MessageType.TEXT,
    formData: formattedData,
    botId,
    metadata: {
      userId: USER_ID,
      channel: CHANNEL,
      conversationId,
      formId,
    },
  };
};

/**
 * Creates a regular text message payload for server submission
 */
export const createTextMessagePayload = (
  text: string,
  botId: string,
  conversationId: string
): MessagePayload => ({
  content: text,
  messageType: MessageType.TEXT,
  botId,
  metadata: {
    userId: USER_ID,
    channel: CHANNEL,
    conversationId,
  },
});

/**
 * Creates a single field form submission payload
 */
export const createSingleFieldFormPayload = (
  fieldName: string,
  value: string,
  botId: string,
  conversationId: string,
  formId?: string
): MessagePayload => ({
  content: JSON.stringify({ [fieldName]: value }),
  messageType: MessageType.TEXT,
  formData: { [fieldName]: value },
  botId,
  metadata: {
    userId: USER_ID,
    channel: CHANNEL,
    conversationId,
    formId,
  },
});

/**
 * Processes server response and converts to ChatMessage array
 */
export const processServerResponse = (responseData: any[]): ChatMessage[] => {
  const botMessages: ChatMessage[] = [];

  responseData.forEach((msg: any) => {
    const label = msg.data?.prompt?.[0]?.label;
    switch (msg.nodeType) {
      case RendererType.FORM:
        if (label) {
          botMessages.push({
            sender: SenderType.BOT,
            nodeType: RendererType.MESSAGE,
            data: {
              text: label,
              type: 'text',
              timestamp: new Date().toISOString(),
            },
          });
        }
        botMessages.push({
          sender: SenderType.BOT,
          nodeType: RendererType.FORM,
          data: msg.data,
        });
        break;

      case RendererType.MESSAGE:
        botMessages.push({
          sender: SenderType.BOT,
          nodeType: RendererType.MESSAGE,
          data: {
            text: msg.data?.text || '',
            type: msg.data?.type || 'text',
            timestamp: msg.data?.timestamp || new Date().toISOString(),
          },
        });
        break;

      case RendererType.FEEDBACK:
        botMessages.push({
          sender: SenderType.BOT,
          nodeType: RendererType.FEEDBACK,
          data: {
            text: msg.data?.prompt || '',
            type: msg.data?.feedbackType || 'text',
            timestamp: msg.data?.timestamp || new Date().toISOString(),
          },
        });
        break;
    }
  });

  return botMessages;
};

/**
 * Determines if a form prompt is a single text field
 */
export const isSingleTextFieldForm = (
  formPrompt: ChatMessage | null
): formPrompt is FormMessage => {
  return (
    formPrompt?.nodeType === RendererType.FORM &&
    formPrompt?.data?.prompt?.length === 1 &&
    !formPrompt.data.prompt[0].fieldType.includes('date')
  );
};

/**
 * Checks if there's an active form that blocks text input
 */
export const hasBlockingForm = (formPrompt: ChatMessage | null): formPrompt is FormMessage => {
  return !!(
    formPrompt?.nodeType === RendererType.FORM &&
    (formPrompt.data.prompt?.length ?? 0) > 0 &&
    !isSingleTextFieldForm(formPrompt)
  );
};
