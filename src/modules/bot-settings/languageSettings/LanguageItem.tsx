import React from 'react';
import { X, Check } from 'lucide-react';
import { LanguageNode } from '../types';
import TagBox from '@/components/TagBox';
import { useTranslation } from 'react-i18next';
import { cn } from '@/lib/utils';

interface LanguageItemProps {
  language: LanguageNode;
  isSelected?: boolean;
  onSelect?: (language: LanguageNode) => void;
  onRemove?: (language: LanguageNode) => void;
  isRemovable?: boolean;
}

const LanguageItem: React.FC<LanguageItemProps> = ({
  language,
  isSelected = false,
  onSelect,
  onRemove,
  isRemovable = false,
}) => {
  const { t } = useTranslation();
  const handleClick = () => {
    if (onSelect && !isSelected) {
      onSelect(language);
    }
  };

  const handleRemove = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent triggering handleClick
    if (onRemove) {
      onRemove(language);
    }
  };

  const isDefaultSelected = language.isDefault && isRemovable; // Only show Default tag for selected languages

  return (
    <div
      className={cn(
        'flex items-center justify-between p-3 rounded-lg cursor-pointer hover:shadow-md transition-shadow',
        (isRemovable || isSelected) && 'border-primary-500 bg-primary-50 border'
      )}
      onClick={handleClick}
    >
      <div className={cn(isDefaultSelected && 'pr-2')}>
        <p className="font-medium text-sm text-foreground">{language.name}</p>
        {!isDefaultSelected && (
          <p className="text-xs text-muted-foreground pt-1">{language.nativeName}</p>
        )}
      </div>
      <div className="flex items-center gap-2">
        {isDefaultSelected && <TagBox text={t('settings.defaultTag')} />}
        {isRemovable && !language.isDefault ? (
          <button
            className="ml-2 p-1 rounded-full hover:bg-secondary-200 text-muted-foreground hover:text-foreground"
            onClick={handleRemove}
          >
            <X className="h-6 w-6" />
          </button>
        ) : (
          isSelected && <Check className="h-6 w-6 text-primary-500" />
        )}
      </div>
    </div>
  );
};

export default LanguageItem;
