import React, { useState, useEffect } from 'react';
import { Tabs } from '@/components/ui/tabs';
import { GenericTabState, GenericItem, GenericFilter } from './types';
import GenericTabNavigation from './GenericTabNavigation';
import GenericSidebar from './GenericSidebar';
import GenericSidebarContainer from './GenericSidebarContainer';
import GenericMainContent from './GenericMainContent';
import { FilterType, ChannelMainTab } from '../Channels/enums';
import { AgentTransferFilterType, AgentTransferMainTab } from '../AgentTransfer/enums';
import { useTabPersistence } from '@/hooks/useTabPersistence';

interface GenericTabbedModuleProps {
  mainTabEnum: typeof ChannelMainTab | typeof AgentTransferMainTab;
  searchTerm?: string;
  onSearch?: (searchTerm: string) => void;
  filterTypeEnum: typeof FilterType | typeof AgentTransferFilterType;
  availableItems: GenericItem[];
  myItems: GenericItem[];
  filters: GenericFilter[];
  mainTabsConfig: { id: string; labelKey: string }[];
  hideTabNavigation?: boolean;
  emptyStateTitleKey: string;
  emptyStateDescriptionKey: string;
  renderMainContent: (item: GenericItem) => React.ReactNode;
  onMainTabChange?: (tab: string) => void;
  queryParamName?: string;
  activeFilter?: string | null;
  onFilterChange?: (filterId: string | null) => void;
  activeMainTab?: string;
}

const GenericTabbedModule: React.FC<GenericTabbedModuleProps> = ({
  mainTabEnum,
  filterTypeEnum,
  availableItems,
  myItems,
  filters,
  mainTabsConfig,
  emptyStateTitleKey,
  emptyStateDescriptionKey,
  renderMainContent,
  hideTabNavigation,
  onMainTabChange,
  searchTerm,
  onSearch,
  activeFilter: propActiveFilter,
  onFilterChange: propOnFilterChange,
  queryParamName,
  activeMainTab,
}) => {
  const [state, setState] = useState<GenericTabState>(() => ({
    mainTab: activeMainTab || mainTabEnum.AVAILABLE,
    searchQuery: searchTerm || '',
    activeFilter: propActiveFilter !== undefined ? propActiveFilter : filterTypeEnum.ALL,
    selectedItem: null,
  }));

  // Use internal state if propOnFilterChange is not provided
  const internalHandleFilterChange = (filterId: string | null) => {
    setState(prev => ({ ...prev, activeFilter: filterId }));
  };

  const currentActiveFilter =
    propActiveFilter !== undefined ? propActiveFilter : state.activeFilter;
  const currentOnFilterChange = propOnFilterChange || internalHandleFilterChange;

  useEffect(() => {
    setState(prev => ({ ...prev, searchQuery: searchTerm || '' }));
  }, [searchTerm]);

  const handleTabChange = (value: string) => {
    setState(prev => ({ ...prev, mainTab: value }));
    onMainTabChange?.(value);
  };

  const currentItems = state.mainTab === mainTabEnum.AVAILABLE ? availableItems : myItems;

  const filteredItems = currentItems.filter(item => {
    const matchesSearch = searchTerm
      ? item.name.toLowerCase().includes(searchTerm.toLowerCase())
      : true;

    const matchesFilter =
      currentActiveFilter === filterTypeEnum.ALL || currentActiveFilter === null
        ? true
        : item.type === currentActiveFilter || item.status === currentActiveFilter;

    return matchesSearch && matchesFilter;
  });

  const [selectedItem, setSelectedItem] = useTabPersistence({
    defaultTab: filteredItems?.[0]?.id,
    queryParamName,
  });

  const pickSelectedItem = queryParamName ? selectedItem : state.selectedItem;
  const handleItemSelect = (itemId: string) => {
    if (queryParamName) {
      setSelectedItem(itemId);
    } else {
      setState(prev => ({ ...prev, selectedItem: itemId }));
    }
  };
  return (
    <Tabs value={state.mainTab} onValueChange={handleTabChange} className="flex flex-1 h-0 w-full">
      <div className="flex flex-col h-full w-[440px] border-r border-tertiary-300">
        {!hideTabNavigation && activeMainTab && (
          <GenericTabNavigation
            activeTab={activeMainTab}
            tabs={mainTabsConfig}
            onTabChange={handleTabChange}
          />
        )}
        <GenericSidebarContainer
          activeTab={state.mainTab}
          items={filteredItems}
          selectedItem={state.selectedItem}
          searchQuery={searchTerm || ''}
          activeFilter={currentActiveFilter}
          filters={filters}
          onItemSelect={handleItemSelect}
          onSearchChange={onSearch || (() => {})}
          onFilterChange={currentOnFilterChange}
          sidebarComponent={GenericSidebar}
          filterTypeEnum={filterTypeEnum}
        />
      </div>
      <GenericMainContent
        selectedItem={currentItems.find(item => item.id === pickSelectedItem)}
        renderContent={renderMainContent}
        emptyStateTitleKey={emptyStateTitleKey}
        emptyStateDescriptionKey={emptyStateDescriptionKey}
      />
    </Tabs>
  );
};

export default GenericTabbedModule;
