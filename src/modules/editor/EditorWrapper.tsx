import FlowsPanel from './widget/flowsPanel';
import NeuraTalkEditor from './neuratalk-editor';
import { useDispatch } from 'react-redux';
import { setActiveFlow } from '@/store/slices/flowsSlice';
import { FlowNode } from '@/types';
import { useAppSelector } from '@/hooks/useRedux';
const EditorWrapper = () => {
  const dispatch = useDispatch();
  const selectedFlow = useAppSelector(state => state.flows.activeFlowId);
  const onFlowSelect = (selectedFlow: FlowNode) => {
    dispatch(setActiveFlow(selectedFlow));
  };

  return (
    <div className="relative transform">
      <FlowsPanel onFlowSelect={onFlowSelect} />
      {selectedFlow && <NeuraTalkEditor id={selectedFlow.appId} />}
    </div>
  );
};

export default EditorWrapper;
