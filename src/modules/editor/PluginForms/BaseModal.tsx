import React, { useState, useEffect, ReactNode, FormEvent, KeyboardEvent } from 'react';
import editIcon from '../assets/common/pluginIcons/edit.svg';
import { getFormSchema } from './utils/schema';
import { getModuleText } from '../utils/config';
import { useTranslation } from 'react-i18next';
import { ModuleData } from '../types';
import { useForm, useFormContext } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';

import { StencilNodesType } from '../utils/constants';
import { Form, FormControl, FormField, FormItem, FormMessage } from '@/components/ui/form';
import { Button } from '@/components/ui/button';
import { PlatformType, Language } from '@/lib/constant';
import RenderButtons from '@/modules/train/components/RenderButtons';
import { Input } from '@/components/ui/input';

interface BaseModalProps {
  id: string;
  type: StencilNodesType;
  moduleData: ModuleData;
  isEdit?: boolean;
  isPublishedEnabled?: boolean;
  handleClose: () => void;
  handleSave: (data: ModuleData, id: string, validate?: boolean) => void;
  children: ReactNode;
}

const BaseModal: React.FC<BaseModalProps> = ({
  id,
  type,
  moduleData,
  isEdit,
  isPublishedEnabled,
  handleClose,
  handleSave,
  children,
}) => {
  const { schema } = getFormSchema(type);
  const defaultModuleData = {
    ...moduleData,
    settings: { ...moduleData.settings, platform: PlatformType.Web, language: Language.English },
  };
  const form = useForm<ModuleData>({
    resolver: zodResolver(schema as z.ZodSchema<ModuleData>),
    defaultValues: defaultModuleData,
  });
  // Create a wrapper for handleSave that merges form data with original moduleData
  const handleSaveWithDefaults = (
    formData: Partial<ModuleData>,
    nodeId: string,
    validate?: boolean
  ) => {
    // Deep merge the form data with the original moduleData to ensure all fields are included
    const completeData: ModuleData = {
      ...moduleData,
      ...formData,
      settings: {
        ...moduleData.settings,
        ...formData.settings,
      },
      process: {
        ...moduleData.process,
        ...formData.process,
      },
      output: {
        ...moduleData.output,
        ...formData.output,
      },
    };
    handleSave(completeData, nodeId, validate);
  };

  return (
    <Form {...form}>
      <BaseModalContent
        id={id}
        type={type}
        isEdit={isEdit}
        isPublishedEnabled={isPublishedEnabled}
        handleClose={handleClose}
        handleSave={handleSaveWithDefaults}
      >
        {children}
      </BaseModalContent>
    </Form>
  );
};

interface BaseModalContentProps extends Omit<BaseModalProps, 'moduleData' | 'children'> {
  children: ReactNode;
}

const hideSubmit = ['whatsapp'];

const BaseModalContent: React.FC<BaseModalContentProps> = ({
  id,
  type,
  isEdit,
  isPublishedEnabled,
  handleClose,
  handleSave,
  children,
}) => {
  const { t } = useTranslation();
  const form = useFormContext<ModuleData>();
  const {
    handleSubmit,
    watch,
    formState: { errors },
  } = form;

  const [isEditable, setIsEditable] = useState(false);

  const nodeName = watch('settings.nodeName');

  useEffect(() => {
    if (isPublishedEnabled && Object.keys(errors).length === 0) {
      handleSubmit(data => handleSave(data, id, true))();
    }
  }, [isPublishedEnabled, errors, handleSubmit, handleSave, id]);

  const submitForm = async (e: FormEvent) => {
    e.preventDefault();
    handleSubmit(
      data => {
        handleSave(data, id);
        handleClose();
      },
      errors => {
        console.log(errors);
      }
    )();
  };

  const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      setIsEditable(false);
    }
  };

  const childrenWithProps = React.Children.map(
    children,
    child => (React.isValidElement(child) ? React.cloneElement(child, {}) : child)
  );

  return (
    <form
      id="setting-form"
      onSubmit={submitForm}
      className={`relative flex flex-col bg-background overflow-hidden h-full`}
    >
      <div className="mt-3">
        <h2 className="text-xs pl-3">
          <span className="text-tertiary-400">{t('common.nodeId')} </span>
          <span className="text-tertiary-600">{id}</span>
        </h2>

        {/* Header */}
        <div className="flex mt-3 pl-3 mb-3">
          {isEditable || nodeName?.length > 25 ? (
            <FormField
              control={form.control}
              name="settings.nodeName"
              render={({ field }) => (
                <FormItem className="flex-1">
                  <FormControl>
                    <Input
                      value={field.value || ''}
                      onChange={field.onChange}
                      onKeyDown={handleKeyDown}
                      placeholder={t('common.enterName')}
                      className="h-10 w-auto border border-tertiary-400 rounded-lg"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          ) : (
            <>
              <span className="mr-2">{nodeName || getModuleText(type)}</span>
              {isEdit && (
                <Button
                  variant="ghost"
                  variantColor="tertiary"
                  className="!px-1"
                  onClick={() => setIsEditable(true)}
                >
                  <img src={editIcon} alt="Edit" className="h-3.5 w-3.5" />
                </Button>
              )}
            </>
          )}
        </div>

        <hr />
      </div>

      {/* Scrollable Content */}
      <div className="flex-1 overflow-y-auto mt-2 space-y-4 pr-1">{childrenWithProps}</div>

      {/* Footer */}
      {!hideSubmit.includes(type) && isEdit && (
        <div className="flex justify-end gap-2 mb-6 pt-4 mr-4">
          <RenderButtons submitButtonText={t('common.save')} handleClose={handleClose} />
        </div>
      )}
    </form>
  );
};

export default BaseModal;
