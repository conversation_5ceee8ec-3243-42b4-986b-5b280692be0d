import React from 'react';
import { useFieldArray, useFormContext } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { Plus, Trash2 } from 'lucide-react';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { Button } from '@/components/ui/button';
import { FormControl, FormField, FormItem } from '@/components/ui/form';
import { FloatingField, FloatingType } from '@/components/ui/floating-label';
import { ModuleData } from '../types';
import { v4 as uuidv4 } from 'uuid';

const conditionOptions = [
  { value: 'Equal To', label: 'Equal To' },
  { value: 'Not Equal To', label: 'Not Equal To' },
  { value: 'Less Than', label: 'Less Than' },
  { value: 'Greater Than', label: 'Greater Than' },
  { value: 'Greater Than or Equal To', label: 'Greater Than or Equal To' },
  { value: 'Less Than or Equal To', label: 'Less Than or Equal To' },
];

const Choice: React.FC = () => {
  const { t } = useTranslation();
  const { control } = useFormContext<ModuleData>();

  const { fields, append, remove } = useFieldArray({
    control,
    name: 'process.match_conditions',
  });

  const handleAddChoice = () => {
    append({
      id: uuidv4().slice(0, 7),
      key: '',
      condition: '',
      value: '',
      moduleId: null,
      coordinates: { x: 100, y: 200 },
    });
  };

  return (
    <div className="flex flex-col h-full px-4">
      <div className="flex-1 overflow-y-auto space-y-2">
        <Accordion type="multiple" defaultValue={fields.map((_, index) => `item-${index}`)}>
          {fields.map((field, index) => (
            <AccordionItem key={field.id} value={`item-${index}`} className="border-b-0">
              <AccordionTrigger className="hover:no-underline py-2">
                <div className="flex items-center justify-between w-full">
                  <span className="text-sm font-medium">
                    {t('editor.choice.title', { number: index + 1 })}
                  </span>
                  {fields.length > 0 && (
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      className="h-7 w-7 text-tertiary-500 hover:text-error-500 hover:bg-error-50"
                      onClick={e => {
                        e.stopPropagation(); // Prevent accordion from toggling
                        remove(index);
                      }}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </AccordionTrigger>
              <AccordionContent className="space-y-4 pt-2">
                <FormField
                  control={control}
                  name={`process.match_conditions.${index}.key`}
                  render={({ field: formField }) => (
                    <FormItem>
                      <FormControl>
                        <FloatingField label={t('editor.choice.keyLabel')} {...formField} />
                      </FormControl>
                    </FormItem>
                  )}
                />
                <FormField
                  control={control}
                  name={`process.match_conditions.${index}.condition`}
                  render={({ field: formField }) => (
                    <FormItem>
                      <FormControl>
                        <FloatingField
                          as={FloatingType.SELECT}
                          label={t('editor.choice.conditionLabel')}
                          options={conditionOptions}
                          {...formField}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
                <FormField
                  control={control}
                  name={`process.match_conditions.${index}.value`}
                  render={({ field: formField }) => (
                    <FormItem>
                      <FormControl>
                        <FloatingField label={t('editor.choice.valueLabel')} {...formField} />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
      </div>

      <div className="pt-2">
        <Button type="button" variant="outline" className="w-full" onClick={handleAddChoice}>
          <Plus className="h-4 w-4 mr-2" />
          {t('editor.choice.addChoice')}
        </Button>
      </div>
    </div>
  );
};

export default Choice;
