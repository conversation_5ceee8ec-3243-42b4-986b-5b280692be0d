import z, { ZodError } from 'zod';

export const validateFieldFunction = async (data: unknown, schema: z.ZodTypeAny) => {
  try {
    return schema.parse(data);
  } catch (error) {
    if (error instanceof ZodError) {
      throw error.errors.map(e => e.path.join('.'));
    }
    throw error;
  }
};

export const useValidation = ({ schema }: { schema: z.ZodTypeAny }) => {
  return { validateFields: (data: unknown) => validateFieldFunction(data, schema) };
};

export const isError = (errors: string[], name: string): boolean => {
  return errors?.some(e => e.includes(name));
};
