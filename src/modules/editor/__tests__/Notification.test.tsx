import { render, screen } from '@/test/utils';
import { FormProvider, useForm } from 'react-hook-form';
import Notification from '../PluginForms/notification';
import { describe, expect, it, vi } from 'vitest';

// Mock useRouterParam
vi.mock('@/hooks/useRouterParam', () => ({
  useBotIdParam: () => ({ botId: 'test-bot-id' }),
}));

// Utility wrapper with react-hook-form context
const Wrapper = ({ children }: { children: React.ReactNode }) => {
  const methods = useForm({
    defaultValues: {
      process: {
        channelData: {
          web: {
            english: {
              notificationChannel: [],
              senderId: '',
              recipientMSISDN: '',
              recipientEmail: '',
              emailSubject: '',
              commonMessage: '',
              emailAddresses: [],
            },
          },
        },
      },
    },
  });
  return <FormProvider {...methods}>{children}</FormProvider>;
};

describe('Notification Component', () => {
  it('renders notification component', () => {
    render(
      <Wrapper>
        <Notification />
      </Wrapper>
    );
    expect(screen.getByText('Select Notification Channel')).toBeInTheDocument();
  });

  it('renders channel and language dropdowns', () => {
    render(
      <Wrapper>
        <Notification />
      </Wrapper>
    );

    // Check for dropdown elements
    const dropdowns = screen.getAllByRole('combobox');
    expect(dropdowns.length).toBeGreaterThan(0);
  });

  it('renders multi-select for notification channels', () => {
    render(
      <Wrapper>
        <Notification />
      </Wrapper>
    );

    // Check for multi-select input by class
    const multiSelectElement = document.querySelector('.multi-select__input');
    expect(multiSelectElement).toBeInTheDocument();
  });

  it('displays web channel by default', () => {
    render(
      <Wrapper>
        <Notification />
      </Wrapper>
    );

    expect(screen.getByText('Web')).toBeInTheDocument();
  });
});
