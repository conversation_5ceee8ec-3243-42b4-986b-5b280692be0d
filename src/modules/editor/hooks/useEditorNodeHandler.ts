import { dia } from 'rappid';
import React, { useCallback } from 'react';
import { getStencilByType } from '@/modules/editor/joint-components/stencil';
import { GraphValidationResult, SettingDetails } from '../types';
import { getModuleIcon, getModuleText, portsIn, portsOut } from '@/modules/editor/utils/config';
import { joinToLeapJSON } from '@/modules/editor/utils/jointJsToLeap';

interface Props {
  graphInstance: dia.Graph | null;
  changeJointJsonToLeapOnDrop: any;
  autoUpdateHandler: any;
  settingDetails: Record<string, any>;
  setError: any;
  setSettingDetails: any;
  modalTypeDetails: any;
}

function useNodeHandler({
  graphInstance,
  changeJointJsonToLeapOnDrop,
  autoUpdateHandler,
  settingDetails,
  setError,
  setSettingDetails,
  modalTypeDetails,
}: Props) {
  const handleChoiceNode = useCallback(
    (updateData: SettingDetails, prevData: SettingDetails, cellId: string) => {
      const deletedNodes = prevData[cellId].process.match_conditions.filter(
        ({ id }: { id: string }) => {
          const index = updateData[cellId].process.match_conditions.findIndex(
            (d: { id: string }) => d.id === id
          );
          return index === -1;
        }
      );

      deletedNodes.forEach(({ id }: { id: string }) => {
        const cell = graphInstance?.getCell(id);
        if (cell) {
          const childs = cell.graph.getSuccessors(cell as dia.Element);
          childs.forEach((child: dia.Element) => {
            if (child.attributes.type !== 'appEnd') {
              child.remove();
            }
          });
        }
      });

      changeJointJsonToLeapOnDrop(graphInstance, updateData);
      setTimeout(() => {
        autoUpdateHandler(updateData, { action: 'update', nodeId: cellId });
      }, 0);
    },
    [graphInstance, changeJointJsonToLeapOnDrop]
  );

  const handleScriptUpdate = useCallback(
    (updateData: SettingDetails) => {
      changeJointJsonToLeapOnDrop(graphInstance, updateData);
    },
    [graphInstance, changeJointJsonToLeapOnDrop]
  );

  const isMaxNodeReached = () => {
    const { modules } = joinToLeapJSON(graphInstance?.toJSON(), settingDetails, graphInstance!);
    const totalNode = Object.keys(modules).length;

    if (totalNode >= 50) {
      setError({ modalId: 'maxNode' });
      return true;
    }
    return false;
  };

  const isValidGraph = useCallback((): GraphValidationResult => {
    if (!graphInstance) {
      return { errorStatus: true, message: 'Graph not initialized' };
    }

    const elements = graphInstance.getElements();
    let isStartPresent = false;
    let isEndPresent = false;
    const errors: string[] = [];

    elements.forEach(el => {
      const {
        attributes: { type },
      } = el;
      if (type === 'appStart') {
        isStartPresent = true;
      }
      if (type === 'appEnd') {
        isEndPresent = true;
      }
    });

    if (!isStartPresent || !isEndPresent) {
      const err = [!isStartPresent ? 'Start' : '', !isEndPresent ? 'End' : ''].filter(Boolean);

      return {
        errorStatus: true,
        message: `Graph is not valid. ${err.join(' & ')} node is not present`,
      };
    }

    elements.forEach(el => {
      const {
        attributes: { type },
      } = el;
      if (type === 'appEnd') return;

      if (graphInstance.isSink(el)) {
        errors.push(type);
      }
    });

    return {
      errorStatus: !!errors.length,
      message: 'Graph is not valid. Flow needs to be properly linked',
      errors,
    };
  }, [graphInstance]);

  const handleSettingsUpdate = useCallback(
    (data: any, id: string, needToCloseModal = true) => {
      if (!modalTypeDetails) return;

      const { type } = modalTypeDetails;

      setSettingDetails((prev: SettingDetails) => {
        const updateData = {
          ...prev,
          [id]: data,
        };

        // Update node name
        if (data.settings.nodeName) {
          const cell = graphInstance?.getCell(id);
          cell?.attr('text/text', data.settings.nodeName);
        }

        // Update node image
        if (data.settings.image) {
          const cell = graphInstance?.getCell(id);
          cell?.attr('image/xlink:href', data.settings.image);
        }

        if (type === 'choice') {
          handleChoiceNode(updateData, prev, id);
        }

        if (type === 'script') {
          handleScriptUpdate(updateData);
        }

        autoUpdateHandler(updateData, {
          action: 'settings',
          nodeId: id,
          type,
        });
        return updateData;
      });
    },
    [modalTypeDetails, graphInstance, handleChoiceNode, handleScriptUpdate, autoUpdateHandler]
  );

  // Node addition
  const handleNodeAdd = useCallback(
    (nodeType: string) => {
      if (isMaxNodeReached() || !graphInstance) return;

      try {
        const nodeInstance = getStencilByType(nodeType);
        console.log('TestingIssue, addNodetype', nodeType, nodeInstance);
        const nodeWidth = 100;
        const nodeHeight = 40;
        const marginY = 50;

        let x = 100;
        let y = 100;

        const elements = graphInstance.getElements();
        if (elements.length > 0) {
          const lastNode = elements[elements.length - 1];
          const lastNodePosition = lastNode.position();
          x = lastNodePosition.x;
          y = lastNodePosition.y + nodeHeight + marginY;

          while (isOverlapping(x, y, nodeWidth, nodeHeight, elements)) {
            y += nodeHeight + marginY;
          }
        }

        const cell = new nodeInstance({
          z: 10,
          attrs: {
            image: { 'xlink:href': getModuleIcon(nodeType) },
            text: { text: getModuleText(nodeType) },
          },
          ...(nodeType !== 'appEnd'
            ? {
                ports: {
                  groups: {
                    in: portsIn,
                    out: portsOut,
                  },
                },
              }
            : {
                ports: {
                  groups: {
                    in: portsIn,
                  },
                },
              }),
        });

        cell.position(x, y);
        graphInstance.addCell(cell);
      } catch (error) {
        // ErrorHandler.handleError(error, "Failed to add node")
      }
    },
    [isMaxNodeReached, graphInstance]
  );

  // Overlap detection
  const isOverlapping = useCallback(
    (x: number, y: number, width: number, height: number, elements: dia.Element[]) => {
      return elements.some(element => {
        const position = element.position();
        const elementWidth = element.size().width;
        const elementHeight = element.size().height;

        const overlappingX = x < position.x + elementWidth && x + width > position.x;
        const overlappingY = y < position.y + elementHeight && y + height > position.y;

        return overlappingX && overlappingY;
      });
    },
    []
  );
  return {
    handleChoiceNode,
    handleScriptUpdate,
    handleNodeAdd,
    isMaxNodeReached,
    handleSettingsUpdate,
  };
}

export default useNodeHandler;
