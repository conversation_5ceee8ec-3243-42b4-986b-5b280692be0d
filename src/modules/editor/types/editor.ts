import type React from 'react';
import type { dia, ui } from 'rappid';
import { StencilNodesType, StencilTabsType } from '../utils/constants';

export interface Position {
  x: number;
  y: number;
}

export interface Size {
  width: number;
  height: number;
}

export interface NodePort {
  id: string;
  group: 'in' | 'out';
  args?: Record<string, any>;
  attrs?: Record<string, any>;
}

export interface NodeData {
  id: string;
  type: string;
  position: Position;
  size?: Size;
  attrs?: Record<string, any>;
  ports?: {
    groups: Record<string, any>;
    items: NodePort[];
  };
  z?: number;
}

export interface LinkData {
  id: string;
  type: 'standard.Link';
  source: {
    id: string;
    port?: string;
    magnet?: string;
  };
  target: {
    id: string;
    port?: string;
  };
  router?: Record<string, any>;
  connector?: Record<string, any>;
  attrs?: Record<string, any>;
  z?: number;
}

export interface GraphData {
  cells: (NodeData | LinkData)[];
}

export interface ModuleSettings {
  nodeName: string;
  timeout?: number;
  title?: string;
  image?: string;
  [key: string]: any;
}

export interface ModuleProcess {
  [key: string]: any;
}

export interface ModuleOutput {
  codeModuleMapping?: Array<{
    code: string;
    moduleId: string;
  }>;
  conditions?: Record<string, any>;
  fallbackcode?: string;
  codeActive?: boolean;
  customCode?: string;
  customCodeIds?: {
    conditionalLink: string[];
  };
}

export interface ModuleData {
  settings: ModuleSettings;
  process: ModuleProcess;
  output: ModuleOutput;
  input?: Record<string, any>;
  type: string;
  typeId?: string;
  coordinates: Position & {
    nodeData?: {
      title: string;
      name: string;
      id: string;
      isEditable: boolean;
      canDelete: boolean;
      status: string;
      moduleType: string;
    };
  };
}

export interface ApplicationData {
  id: string;
  name: string;
  desc: string;
  status: string;
  svg?: string;
  appData: {
    modules: LeapModules;
    startId: string;
  };
}

export interface ModalTypeDetails {
  id: string;
  type: StencilNodesType;
  top: number;
  left: number;
}

export interface AutoSuggestionPosition {
  top: number;
  left: number;
  element: any;
}

export interface UndoRedoState {
  payload: ApplicationData;
  action: string;
  nodeId?: string;
  type?: string;
}

export interface ValidationError {
  type: string;
  errorDetails: any;
}

export interface GraphValidationResult {
  errorStatus: boolean;
  message: string;
  errors?: string[];
}

export interface StencilConfig {
  type: StencilNodesType;
  enabled?: boolean;
}

export interface NodeGroupConfig {
  [category: string]: StencilConfig[];
}

export interface TabDetail {
  key: string;
  label: string;
}

export interface SimulatorRef {
  current: {
    openParamsModal: () => void;
    stopSimulator: () => void;
    retrySimulator: () => void;
    nextNode: () => void;
    handleListener: (callback: (status: boolean) => void) => void;
  } | null;
}

export interface EditorProps {
  isStencilEnabled?: boolean;
  isToolBarEnabled?: boolean;
  isEdit?: boolean;
  isNavbarEnabled?: boolean;
  jsonDetails?: ApplicationData;
  updateParentPaperInstance?: (paper: dia.Paper) => void;
}

export interface StencilProps {
  paper: dia.Paper;
  graph: dia.Graph;
  handleNodeAdd: (nodeType: string) => void;
  isMaxNodeReached: () => boolean;
}

export interface FormProps {
  handleClose: (isValidationNotNeeded?: boolean) => void;
  type: string;
  id: string;
  modalTypeDetails: ModalTypeDetails;
  startUrl: string;
  moduleData: ModuleData;
  handleSave: (data: ModuleData, id: string, needToCloseModal?: boolean) => void;
  isEdit: boolean;
  isPublishedEnabled: boolean;
}

export interface NavBarProps {
  name: string;
  desc: string;
  updateAppJson: () => void;
  startUrl: string;
  updateAppName: (data: { appName: string; appDesc: string }) => void;
  selectedTab: string;
  autoUpdateLoader: boolean;
  simulatorInstance: SimulatorRef;
  updateSimulatorStatus: (status: boolean) => void;
  status: string;
  isReadOnly: boolean;
  validateSimulator: () => Promise<boolean>;
}

export interface ToolkitProps {
  scrollInstance: any;
  canvasWithPallette: React.RefObject<HTMLDivElement>;
  handleRedo: () => void;
  handleUndo: () => void;
  canUndo: boolean;
  canRedo: boolean;
  formatGraph: (graph: dia.Graph) => void;
  isEdit: boolean;
}

export interface SimulatorProps {
  graph: dia.Graph;
  scroller: any;
  paper: dia.Paper;
  startUrl: string;
  changeJointJsonToLeapOnDrop: (graph: dia.Graph, updateData?: Record<string, ModuleData>) => void;
  settingDetails: Record<string, ModuleData>;
}

export interface JointJSCell {
  type: string;
  id: string;
  position: { x: number; y: number };
  source?: { id: string; port?: string };
  target?: { id: string; port?: string };
  attributes?: any;
}

export interface LeapModules {
  [key: string]: ModuleData & {
    type: string;
    coordinates: {
      x: number;
      y: number;
      nodeData: {
        title: string;
        name: string;
        id: string;
        isEditable: boolean;
        canDelete: boolean;
        status: string;
        moduleType: string;
      };
    };
    isChoiceLinked?: boolean;
    isScriptLinked?: boolean;
  };
}

export interface JointJSData {
  nodes: { cells: any[] };
  jsonData: Record<string, ModuleData>;
}

export interface StencilState {
  collapseStatus: boolean;
  minimizeStatus: boolean;
  floatPosition: 'left' | 'right';
  collapseCache: Position | null;
  position: Position;
  currentTab: StencilTabsType;
  search: string;
  isExpandNodeModalOpen: boolean;
}

export type SettingDetails = Record<string, any>;

export interface EventHandlerContext {
  paper: dia.Paper;
  graph: dia.Graph;
  scroller?: ui.PaperScroller;
  isEdit: boolean;
  modalTypeDetails: ModalTypeDetails | null;
  setCurrentElementView: React.Dispatch<React.SetStateAction<dia.ElementView | null>>;
  setModalTypeDetails: React.Dispatch<React.SetStateAction<ModalTypeDetails | null>>;
  setHoverElement: React.Dispatch<React.SetStateAction<Position | null>>;
  hoverElement: Position | null;
  changeJointJsonToLeapOnDrop: (graph: dia.Graph, updateData?: null) => void;
  autoUpdateHandler: (
    setting?: SettingDetails | null,
    updateActionDetails?: { action: string; nodeId: string; type?: string } | null,
    appName?: string,
    appDesc?: string,
    updateAppDetailsStatus?: boolean
  ) => void;
  removeLastUndoState: () => void;
}
