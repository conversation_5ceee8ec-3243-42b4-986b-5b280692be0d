import { v4 as uuidv4 } from 'uuid';
import type { ModuleData, LeapModules, JointJSData, ChoiceDetails, NodePort } from '../types';
import { getNodeConstant, linksConstant } from './jsonConstant';
import { jointJsEditorTheme } from './constants';

export const leapToJointJSON = (modules: LeapModules): JointJSData => {
  if (!modules || Object.keys(modules).length === 0) {
    return {
      nodes: { cells: [] },
      jsonData: {},
    };
  }
  const modulesKeys = Object.keys(modules);
  const cells: any[] = [];
  const jsonData: Record<string, ModuleData> = {};

  modulesKeys.forEach((id, index) => {
    const { type, coordinates, output = {}, settings, process } = modules[id];

    const { x, y } = coordinates;
    const customCodeIds = JSON.parse(JSON.stringify(output?.customCodeIds || {}));
    let conditionalLink = customCodeIds?.conditionalLink || [];

    const cell = getNodeConstant(
      { type, name: settings.nodeName, image: settings?.image },
      { x, y },
      id,
      index + 1
    );

    jsonData[id] = {
      output,
      settings,
      process,
      coordinates,
      type,
    };

    if (type === 'choice') {
      const { match_conditions, no_match_module_id } =
        modules[id].process;

      const portId = uuidv4().slice(0, 7);
      const portId1 = uuidv4().slice(0, 7);
      const portInId = uuidv4().slice(0, 7);

      cell.ports = {
        ...cell.ports,
        items: [
          {
            id: portId,
            group: 'out',
            args: { x: 12 },
            attrs: {
              portBody: {
                fill: jointJsEditorTheme.successColor,
                stroke: jointJsEditorTheme.successColor,
              },
            },
          } as NodePort,
          {
            id: portId1,
            group: 'out',
            args: { x: 40 },
            attrs: {
              portBody: {
                fill: jointJsEditorTheme.errorColor,
                stroke: jointJsEditorTheme.errorColor,
              },
            },
          } as NodePort,
          {
            group: 'in',
            id: portInId,
          } as NodePort,
        ],
      };
      match_conditions.forEach((matchCondition: any, index: number) => {
        const { id: optionId, ...rest } = matchCondition;

        if (!rest.key) {
          return null;
        }

        const { coordinates = {}, condition = '', value = '' } = match_conditions[index - 1] ?? {};

        const maxLen = Math.max(condition.length, value.length);
        const prevNodeX = coordinates.x ?? x;
        const nodeX = index === 0 ? x + 150 * (index + 1) : prevNodeX + maxLen * 9 + 80;

        const positionDetails = rest.coordinates?.x ? rest.coordinates : { x: nodeX, y: y + 100 };

        const choiceNodeCell = getNodeConstant(
          { type: 'choiceOption' },
          positionDetails,
          optionId,
          index + 1,
          rest as ChoiceDetails
        );

        const choiceOptionPortId = uuidv4().slice(0, 7);

        choiceNodeCell.ports = {
          ...choiceNodeCell.ports,
          items: [
            {
              group: 'out',
              id: choiceOptionPortId,
            },
          ],
        };

        cells.push(choiceNodeCell);

        // Create link from choice to choice option
        const linksConstantCopy = {
          ...linksConstant,
          id: uuidv4().slice(0, 7),
          source: {
            ...linksConstant.source,
            id,
            port: portId,
          },
          target: {
            ...linksConstant.target,
            id: optionId,
          },
        };
        cells.push(linksConstantCopy);

        // Create link from choice option to next module
        if (rest.moduleId) {
          const linksConstantChoiceOption = {
            ...linksConstant,
            id: uuidv4().slice(0, 7),
            source: {
              ...linksConstant.source,
              id: optionId,
              port: choiceOptionPortId,
            },
            target: {
              ...linksConstant.target,
              id: rest.moduleId,
            },
          };
          cells.push(linksConstantChoiceOption);
        }
      });

      // Handle no match condition
      if (no_match_module_id) {
        const linksConstantChoiceOption = {
          ...linksConstant,
          id: uuidv4().slice(0, 7),
          source: {
            ...linksConstant.source,
            id: id,
            port: portId1,
          },
          target: {
            ...linksConstant.target,
            id: no_match_module_id,
          },
        };
        cells.push(linksConstantChoiceOption);
      }
    }

    // Handle conditional links
    if (conditionalLink.length) {
      const portId = uuidv4().slice(0, 7);
      const links = conditionalLink.map((targetId: string) => {
        const linksConstantCopy = {
          ...linksConstant,
          id: uuidv4().slice(0, 7),
          source: {
            ...linksConstant.source,
            id,
            port: portId,
          },
          target: {
            ...linksConstant.target,
            id: targetId,
          },
        };

        cell.ports = {
          ...cell.ports,
          items: [
            {
              group: 'out',
              id: portId,
            },
            {
              group: 'in',
              id: uuidv4().slice(0, 7),
            },
          ],
        };

        return JSON.parse(JSON.stringify(linksConstantCopy));
      });

      if (links.length) {
        cells.push(...links);
      }
    }

    cells.push(cell);
  });

  // Set target ports for links
  cells.forEach((cellData: any, index: number) => {
    const { type, target } = cellData;
    if (type === 'standard.Link') {
      const nodeIndex = cells.findIndex(({ id: nodeId }) => nodeId === target.id);

      const portInId =
        cells[nodeIndex]?.ports?.items?.find(({ group }: { group: string }) => group === 'in')
          ?.id ?? uuidv4().slice(0, 7);

      if (nodeIndex !== -1) {
        cells[index].target = {
          ...cells[index].target,
          port: portInId,
        };

        cells[nodeIndex].ports.items = cells[nodeIndex].ports.items.filter(
          ({ group }: { group: string }) => group !== 'in'
        );

        cells[nodeIndex].ports.items.push({
          group: 'in',
          id: portInId,
        });
      }
    }
  });

  return {
    nodes: { cells },
    jsonData,
  };
};
