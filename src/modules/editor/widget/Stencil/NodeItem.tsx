import React, { useCallback } from 'react';
import { cn } from '@/lib/utils';
import { getModuleIcon, getModuleText } from '@/modules/editor/utils/config';
import { StencilNodesType } from '../../utils/constants';

interface NodeItemProps {
  type: StencilNodesType;
  startDrag: (event: React.MouseEvent, type: string) => void;
  isComingSoon?: boolean;
}

const NodeItem: React.FC<NodeItemProps> = ({ type, startDrag, isComingSoon }) => {
  const handlePointerDown = useCallback(
    (event: React.MouseEvent) => {
      if (isComingSoon) return;

      event.preventDefault();
      startDrag(event, type);
    },
    [startDrag, type, isComingSoon]
  );

  return (
    <div
      className={cn(
        'flex flex-col items-center gap-3 rounded-lg hover:border-primary-300 transition-all cursor-pointer bg-background',
        { 'opacity-50 select-none': isComingSoon }
      )}
      onMouseDown={handlePointerDown}
    >
      <div className="rounded-2xl flex items-center justify-center bg-primary-500/5 p-3 text-primary-500">
        {typeof getModuleIcon(type) === 'string' ? (
          <img
            src={getModuleIcon(type) as string}
            className="text-primary-500 !w-6 !h-6"
            alt={type}
          />
        ) : (
          <div className="border py-3 px-3 text-primary-500 rounded-2xl">
            {React.createElement(getModuleIcon(type) as React.ElementType, { size: 20 })}
          </div>
        )}
      </div>

      <p className="text-xs text-center text-primary-500">{getModuleText(type)}</p>
    </div>
  );
};

export default NodeItem;
