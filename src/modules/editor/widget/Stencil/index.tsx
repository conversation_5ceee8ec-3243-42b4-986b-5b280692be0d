import React, { useState, useMemo } from 'react';
import { Rnd } from 'react-rnd';
import StencilHeader from './StencilHeader';
import StencilTabs from './StencilTabs';
import NodeList from './NodeList';
import { StencilProps } from '../../types';
import { cn } from '@/lib/utils';
import useStencil from '@/modules/editor/hooks/useStencil';
import { useAppSelector } from '@/hooks/useRedux';

const Stencil: React.FC<StencilProps> = ({
  paper,
  graph,
  handleNodeAdd,
  isMaxNodeReached,
}) => {
  const { state, dragRef, getStencilDimensions, handleSearch, handleTabChange, startDrag } =
    useStencil({
      paper,
      graph,
      isMaxNodeReached,
      handleNodeAdd,
    });

  const [isCollapsed, setIsCollapsed] = useState(false);
  const toggleCollapse = () => {
    setIsCollapsed(prev => !prev);
  };

  const { showPreview } = useAppSelector(state => state.ui);

  const canvasElement = useMemo(() => {
    return document.getElementsByClassName('canvas')[0];
  }, []);

  if (!canvasElement) {
    return null;
  }

  return (
    <Rnd
      position={state.position}
      bounds=".canvas"
      onDragStop={state.onControlledDragStop}
      enableResizing={false}
      size={getStencilDimensions}
      disableDragging={true}
      ref={dragRef}
      className="!w-fit !h-fit"
    >
      <div className={cn({ 'right-[125%]': showPreview })}>
        <div
          className={cn('w-72 bottom-2.5 shadow-md rounded-lg bg-background', {
            'h-16 translate-x-44 w-28': isCollapsed,
            'h-auto translate-x-0': !isCollapsed,
            '-translate-x-0': isCollapsed && showPreview,
          })}
        >
          <div className="h-auto max-h-full relative">
            <StencilHeader
              handleSearch={handleSearch}
              isCollapsed={isCollapsed}
              toggleCollapse={toggleCollapse}
            />
            {!isCollapsed && (
              <>
                <StencilTabs currentTab={state.currentTab} handleTabChange={handleTabChange} />
                <NodeList
                  collapseStatus={isCollapsed}
                  currentTab={state.currentTab}
                  search={state.search}
                  startDrag={startDrag}
                />
              </>
            )}
          </div>
        </div>
      </div>
    </Rnd>
  );
};

export default Stencil;
