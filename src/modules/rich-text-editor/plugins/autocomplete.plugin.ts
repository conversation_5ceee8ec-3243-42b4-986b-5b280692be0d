
import { Plugin, Plugin<PERSON><PERSON> } from 'prosemirror-state';
import { EditorView } from 'prosemirror-view';
import { Schema } from 'prosemirror-model';

export const autocompletePluginKey = new PluginKey('autocomplete');

interface AutocompleteState {
  active: boolean;
  query: string;
  suggestions: string[];
  from: number;
  to: number;
  selectedIndex: number; // To track selected suggestion for keyboard navigation
}

const fetchSuggestions = async (query: string): Promise<any[]> => {
  // This URL needs to be dynamic or configured. For now, hardcode the example.
  const apiUrl = 'http://localhost:3000/api/v1/apps/cf6dac25-81e8-4e06-918d-14b415fa59f3/autocomplete/globalContext';
  try {
    const response = await fetch(apiUrl);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching autocomplete suggestions:', error);
    return [];
  }
};

// Utility to flatten the nested API response into dot-separated paths
const flattenSuggestions = (data: any[], prefix: string = ''): string[] => {
  let suggestions: string[] = [];
  data.forEach(item => {
    const currentPath = prefix ? `${prefix}.${item.name}` : item.name;
    suggestions.push(currentPath);
    if (item.children && item.children.length > 0) {
      suggestions = suggestions.concat(flattenSuggestions(item.children, currentPath));
    }
    if (item.valueSchema && item.valueSchema.length > 0) {
      item.valueSchema.forEach((schemaItem: any) => {
        suggestions.push(`${currentPath}.${schemaItem.name}`);
      });
    }
  });
  return suggestions;
};

export const autocompletePlugin = (schema: Schema) => {
  return new Plugin({
    key: autocompletePluginKey,

    state: {
      init() {
        return {
          active: false,
          query: '',
          suggestions: [],
          from: 0,
          to: 0,
          selectedIndex: -1,
        } as AutocompleteState;
      },
      apply(tr, value: AutocompleteState, oldState, newState) {
        const meta = tr.getMeta(autocompletePluginKey);
        if (meta) {
          return { ...value, ...meta };
        }
        // If the document changes and the plugin was active,
        // we need to adjust 'from' and 'to' or deactivate if the trigger text is gone.
        if (value.active) {
          const { doc, selection } = newState;
          const $cursor = selection.$head;
          const textBeforeCursor = doc.textBetween($cursor.before(), $cursor.pos);
          const match = textBeforeCursor.match(/\{\{([a-zA-Z0-9._]*)$/);

          if (match) {
            const newFrom = $cursor.pos - match[0].length;
            const newTo = $cursor.pos;
            return { ...value, from: newFrom, to: newTo, query: match[1] };
          } else {
            return { ...value, active: false, query: '', suggestions: [], selectedIndex: -1 };
          }
        }
        return value;
      },
    },

    props: {
      handleTextInput(view: EditorView, from: number, to: number, text: string): boolean {
        const { doc, selection } = view.state;
        const $cursor = selection.$head;
        const textBeforeCursor = doc.textBetween($cursor.before(), $cursor.pos);
        const match = textBeforeCursor.match(/\{\{([a-zA-Z0-9._]*)$/);

        if (match) {
          const query = match[1];
          const startPos = $cursor.pos - match[0].length;
          const endPos = $cursor.pos;

          // Fetch suggestions and update plugin state
          fetchSuggestions(query).then(apiData => {
            const allSuggestions = flattenSuggestions(apiData);
            const filteredSuggestions = allSuggestions.filter(s => s.startsWith(query));

            view.dispatch(
              view.state.tr.setMeta(autocompletePluginKey, {
                active: true,
                query,
                suggestions: filteredSuggestions,
                from: startPos,
                to: endPos,
                selectedIndex: -1, // Reset selection on new query
              } as AutocompleteState)
            );
          });
          return false; // Allow text input to proceed
        } else {
          const pluginState = autocompletePluginKey.getState(view.state) as AutocompleteState;
          if (pluginState.active) {
            // If active but no longer matching '{{', deactivate
            view.dispatch(
              view.state.tr.setMeta(autocompletePluginKey, {
                active: false,
                query: '',
                suggestions: [],
                from: 0,
                to: 0,
                selectedIndex: -1,
              } as AutocompleteState)
            );
          }
        }
        return false; // Not handled
      },

      handleKeyDown(view: EditorView, event: KeyboardEvent): boolean {
        const pluginState = autocompletePluginKey.getState(view.state) as AutocompleteState;

        if (pluginState.active && pluginState.suggestions.length > 0) {
          if (event.key === 'ArrowDown') {
            event.preventDefault();
            const newIndex = (pluginState.selectedIndex + 1) % pluginState.suggestions.length;
            view.dispatch(
              view.state.tr.setMeta(autocompletePluginKey, {
                selectedIndex: newIndex,
              } as Partial<AutocompleteState>)
            );
            return true;
          } else if (event.key === 'ArrowUp') {
            event.preventDefault();
            const newIndex =
              (pluginState.selectedIndex - 1 + pluginState.suggestions.length) %
              pluginState.suggestions.length;
            view.dispatch(
              view.state.tr.setMeta(autocompletePluginKey, {
                selectedIndex: newIndex,
              } as Partial<AutocompleteState>)
            );
            return true;
          } else if (event.key === 'Enter') {
            event.preventDefault();
            const selectedSuggestion =
              pluginState.selectedIndex !== -1
                ? pluginState.suggestions[pluginState.selectedIndex]
                : pluginState.suggestions[0]; // Default to first if nothing selected

            if (selectedSuggestion) {
              const { tr } = view.state;
              const newText = `${selectedSuggestion}}}`; // Add closing curly braces
              tr.replaceRangeWith(pluginState.from, pluginState.to, schema.text(newText));
              view.dispatch(tr);

              // Deactivate autocomplete after selection
              view.dispatch(
                view.state.tr.setMeta(autocompletePluginKey, {
                  active: false,
                  query: '',
                  suggestions: [],
                  from: 0,
                  to: 0,
                  selectedIndex: -1,
                } as AutocompleteState)
              );
            }
            return true;
          } else if (event.key === 'Escape') {
            event.preventDefault();
            view.dispatch(
              view.state.tr.setMeta(autocompletePluginKey, {
                active: false,
                query: '',
                suggestions: [],
                from: 0,
                to: 0,
                selectedIndex: -1,
              } as AutocompleteState)
            );
            return true;
          }
        }
        return false;
      },
    },
  });
};
