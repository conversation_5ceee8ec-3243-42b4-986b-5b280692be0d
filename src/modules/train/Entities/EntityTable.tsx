import React, { useState } from 'react';
import AddModal from '../components/AddModal';
import AddEntityForm from './AddEntityForm';
import DeleteConfirmationModal from '@/components/DeleteConfirmationModal';
import { useDeleteEntityMutation } from '@/store/api';
import { useToast } from '@/hooks/use-toast';
import SuccessToastMessage from '@/components/SuccessToastMessage';
import { useTranslation } from 'react-i18next';
import { Pencil, Trash2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Entity, EntityType, ModalState } from '@/types';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { cn } from '@/lib/utils';

interface EntityTableProps {
  entities: Entity[];
  botId: string;
}

const EntityTable: React.FC<EntityTableProps> = ({ entities, botId }) => {
  const { t } = useTranslation();
  const { toast } = useToast();
  const [modalState, setModalState] = useState<ModalState<Entity> | null>(null);

  const [deleteEntity] = useDeleteEntityMutation();

  const handleEdit = (entity: Entity) => {
    setModalState({ type: 'edit', item: entity });
  };

  const handleDelete = (entity: Entity) => {
    setModalState({ type: 'delete', item: entity });
  };

  const confirmDelete = async () => {
    if (!modalState || modalState.type !== 'delete') return;
    try {
      await deleteEntity({ id: modalState.item.id }).unwrap();
      toast({
        title: <SuccessToastMessage message={t('entities.entityDeleted')} />,
      });
    } catch (error: any) {
      console.error('Failed to delete entity:', error);
      toast({
        title: t('common.error'),
        description: error,
        variant: 'destructive',
      });
    } finally {
      handleCloseModal();
    }
  };

  const handleCloseModal = () => {
    setModalState(null);
  };

  return (
    <div className="rounded-lg h-full">
      <Table wrapperClassName="h-full">
        <TableHeader className="[&_tr]:border-0 ">
          <TableRow>
            <TableHead>{t('entities.table.name')}</TableHead>
            <TableHead>{t('entities.table.type')}</TableHead>
            <TableHead>{t('entities.table.value')}</TableHead>
            <TableHead className="text-right pr-7">{t('entities.table.action')}</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody className="[&_tr]:border-0 overflow-y-auto">
          {entities.map((entity, index) => (
            <TableRow
              key={entity.id}
              className={cn(
                'rounded-lg hover:bg-transparent text-xs',
                index % 2 === 0 ? 'bg-tertiary-100 hover:bg-tertiary-100' : ''
              )}
            >
              <TableCell>{entity.name}</TableCell>
              <TableCell>{entity.type}</TableCell>
              <TableCell
                className={cn('text-tertiary-600 pl-5', {
                  'pl-8': entity.type !== EntityType.REGEX,
                })}
              >
                {entity.metadata?.value || '-'}
              </TableCell>
              <TableCell className="text-right">
                <div className="flex items-center justify-end pr-2">
                  <Button
                    variant="ghost"
                    variantColor="tertiary"
                    size="icon"
                    className="h-8 w-8"
                    onClick={() => handleEdit(entity)}
                    aria-label="Edit"
                  >
                    <Pencil className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8 text-error-600 hover:text-error-700"
                    onClick={() => handleDelete(entity)}
                    aria-label="Delete"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      {modalState?.type === 'edit' && (
        <AddModal
          title={t('entities.editTitle')}
          className="sm:max-w-[40rem]"
          open={true}
          onOpenChange={handleCloseModal}
        >
          <AddEntityForm botId={botId} onClose={handleCloseModal} entity={modalState.item} />
        </AddModal>
      )}

      <DeleteConfirmationModal
        isOpen={modalState?.type === 'delete'}
        onClose={handleCloseModal}
        onConfirm={confirmDelete}
        title={t('entities.confirmDeleteTitle')}
        description={t('entities.deleteConfirmationMessage')}
      />
    </div>
  );
};

export default EntityTable;
