import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useGetEntitiesQuery } from '@/store/api';
import { Button } from '@/components/ui/button';
import { Search, Filter } from 'lucide-react';
import EmptyState from '@/components/EmptyState';
import AddModal from '../components/AddModal';
import AddEntityForm from './AddEntityForm';
import EntityTable from './EntityTable';
import { useBotIdParam } from '@/hooks/useRouterParam';
import {
  PaginationProvider,
  usePagination,
  PaginationRenderItems,
  PaginationLoader,
  PaginationEmptyState,
  PaginationError,
  PaginationFilter,
  PaginationSearch,
} from '@/components/Pagination';
import { Entity } from '@/types';

const EntitiesTab: React.FC = () => {
  const { t } = useTranslation();
  const { botId } = useBotIdParam();
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);

  const pagination = usePagination({
    useQueryHook: query =>
      useGetEntitiesQuery(
        {
          botId,
          ...query,
        },
        { skip: !botId }
      ),
  });

  const onClose = () => {
    setIsAddModalOpen(false);
  };

  const addEntityTrigger = (
    <AddModal
      title={t('entities.addTitle')}
      open={isAddModalOpen}
      onOpenChange={setIsAddModalOpen}
      className="max-w-[44rem]"
      trigger={
        <Button variant="outline" className="mt-4">
          {t('entities.addTitle')}
        </Button>
      }
    >
      <AddEntityForm onClose={onClose} botId={botId} />
    </AddModal>
  );

  return (
    <div className="flex flex-col px-6 h-full">
      <div className="pt-4">
        <h2 className="text-lg mb-4">{t('entities.title')}</h2>
      </div>
      <PaginationProvider value={pagination}>
        <div className="px-4 py-2 mx-4 gap-4 flex flex-col flex-1 h-0 rounded-lg shadow-floating bg-background">
          <div className="flex items-center justify-between gap-4">
            <div className="flex items-center gap-2 flex-1">
              <div className="relative flex-1 max-w-sm">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-tertiary-400 h-4 w-4" />
                <PaginationSearch placeholder={t('common.search')} className="pl-10" />
              </div>
              <PaginationFilter>
                <Button
                  variant="outline"
                  variantColor="tertiary"
                  size="icon"
                  aria-label="Filter entities"
                >
                  <Filter className="h-4 w-4" />
                </Button>
              </PaginationFilter>
            </div>
            {addEntityTrigger}
          </div>

          <div className="flex-1 h-0 border-t pt-2 flex flex-col">
            <PaginationRenderItems<Entity>
              className="flex-1 h-0"
              renderItems={items => <EntityTable entities={items} botId={botId} />}
            />
            <PaginationLoader />
            <PaginationEmptyState>
              <div className="flex flex-col h-full items-center justify-center">
                <EmptyState
                  className="w-full"
                  title={t('entities.startAdding')}
                  description={t('common.nothingToShow')}
                >
                  {addEntityTrigger}
                </EmptyState>
              </div>
            </PaginationEmptyState>
            <PaginationError />
          </div>
        </div>
      </PaginationProvider>
    </div>
  );
};

export default EntitiesTab;
