import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Entity } from '@/types';
import { Search, X } from 'lucide-react';
import { useTranslation } from 'react-i18next';

const EntityDropdown: React.FC<{
  entities: Entity[];
  selectedText: string;
  onSelect: (entity: Entity) => void;
  onClose: () => void;
  searchTerm: string;
  setSearchTerm: (term: string) => void;
}> = ({ entities, selectedText, onSelect, onClose, searchTerm, setSearchTerm }) => {
  const { t } = useTranslation();
  //TODO: need to implement infinite scrolling for this
  return (
    <div className="absolute top-full left-0 right-0 mt-1 bg-background border border-tertiary-200 rounded-md shadow-lg z-50 max-h-60 overflow-y-auto">
      <div className="p-2 border-b border-tertiary-100">
        <div className="relative">
          <Search className="absolute left-2 top-1/2 -translate-y-1/2 h-4 w-4 text-tertiary-400" />
          <Input
            value={searchTerm}
            onChange={e => setSearchTerm(e.target.value)}
            placeholder={t('entities.searchEntities')}
            className="pl-8 pr-8 h-8"
          />
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="absolute right-1 top-1/2 -translate-y-1/2 h-6 w-6 p-0"
          >
            <X className="h-3 w-3" />
          </Button>
        </div>
        {selectedText && (
          <div className="mt-2 text-xs text-tertiary-600">
            {t('entities.selected')}: &ldquo;<span className="font-medium">{selectedText}</span>
            &rdquo;
          </div>
        )}
      </div>
      <div className="max-h-40 overflow-y-auto">
        {entities.length === 0 ? (
          <div className="p-3 text-sm text-tertiary-500 text-center">
            {t('entities.noEntitiesFound')}
          </div>
        ) : (
          entities.map(entity => (
            <button
              key={entity.id}
              onClick={() => onSelect(entity)}
              className="w-full text-left p-3 hover:bg-tertiary-50 border-b border-tertiary-50 last:border-b-0 focus:outline-none focus:bg-tertiary-50"
            >
              <div className="font-medium text-sm">{entity.name}</div>
              <div className="text-xs text-tertiary-500">{entity.type}</div>
            </button>
          ))
        )}
      </div>
    </div>
  );
};

export default EntityDropdown;
