import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { describe, it, expect, vi } from 'vitest';
import EntityHighlightInput from '../EntityHighlightInput';

// Mock the entire API module
vi.mock('@/store/api', () => ({
  useGetEntitiesQuery: vi.fn(() => ({
    data: mockApiResponse,
    isLoading: false,
    error: null,
  })),
}));

// Mock UI components
vi.mock('@/components/ui/input', () => ({
  Input: React.forwardRef<HTMLInputElement, any>(
    ({ value, onChange, placeholder, onMouseUp, onKeyUp, ...props }, ref) => (
      <input
        ref={ref}
        value={value}
        onChange={onChange}
        onMouseUp={onMouseUp}
        onKeyUp={onKeyUp}
        placeholder={placeholder}
        {...props}
      />
    )
  ),
}));

vi.mock('@/components/ui/button', () => ({
  Button: ({ children, onClick, ...props }: any) => (
    <button data-testid="button" onClick={onClick} {...props}>
      {children}
    </button>
  ),
}));

// Mock the entity API
const mockEntities = [
  {
    id: '1',
    name: 'PersonName',
    type: 'TEXT' as const,
    botId: 'bot1',
    intentId: null,
    metadata: {},
  },
  {
    id: '2',
    name: 'EmailAddress',
    type: 'EMAIL' as const,
    botId: 'bot1',
    intentId: 'intent1',
    metadata: {},
  },
];

// Mock the API response
const mockApiResponse = {
  data: {
    items: mockEntities,
    totalCount: 2,
  },
};

// Create a simple mock store
const createMockStore = () => {
  return configureStore({
    reducer: {
      // Simple reducer for testing
      test: (state = {}) => state,
    },
  });
};

describe('EntityHighlightInput', () => {
  const defaultProps = {
    value: '',
    onChange: vi.fn(),
    botId: 'bot1',
    placeholder: 'Enter utterance text...',
  };

  const renderWithProvider = (props = {}) => {
    const store = createMockStore();
    return render(
      <Provider store={store}>
        <EntityHighlightInput {...defaultProps} {...props} />
      </Provider>
    );
  };

  it('renders contentEditable with placeholder', () => {
    renderWithProvider();

    const contentEditable = screen.getByRole('textbox');
    expect(contentEditable).toBeInTheDocument();
    expect(contentEditable).toHaveAttribute('data-placeholder', 'Enter utterance text...');
  });

  it('displays current value without entity markup', () => {
    const textWithEntity = 'Hello [John](PersonName_1_TEXT)';
    renderWithProvider({ value: textWithEntity });

    // Should display the text without markup in contentEditable
    const contentEditable = screen.getByRole('textbox');
    expect(contentEditable.textContent).toBe('Hello John');
  });

  it('calls onChange when content changes', () => {
    const mockOnChange = vi.fn();
    renderWithProvider({ onChange: mockOnChange });

    const contentEditable = screen.getByRole('textbox');

    // Simulate typing in contentEditable
    contentEditable.textContent = 'New text';
    fireEvent.input(contentEditable);

    expect(mockOnChange).toHaveBeenCalledWith('New text');
  });

  it('renders without crashing with entity markup', () => {
    const textWithEntity =
      'Hello [John](PersonName_1_TEXT) and [<EMAIL>](EmailAddress_2_EMAIL)';
    renderWithProvider({ value: textWithEntity });

    // Should display the text without markup in contentEditable
    const contentEditable = screen.getByRole('textbox');
    expect(contentEditable.textContent).toBe('Hello <NAME_EMAIL>');
  });

  it('handles empty entity response gracefully', () => {
    renderWithProvider({ value: 'Hello world' });

    expect(screen.getByRole('textbox')).toBeInTheDocument();
  });

  it('renders entities inline in contentEditable', () => {
    const textWithEntity = 'Hello [John](PersonName_1_TEXT)';
    renderWithProvider({ value: textWithEntity });

    // The contentEditable should contain the entity text
    const contentEditable = screen.getByRole('textbox');
    expect(contentEditable).toBeInTheDocument();
    expect(contentEditable.textContent).toContain('John');
  });
});
