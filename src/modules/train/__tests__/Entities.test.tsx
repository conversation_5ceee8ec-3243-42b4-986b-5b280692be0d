import { render, screen, fireEvent, waitFor } from '@/test/utils';
import userEvent from '@testing-library/user-event';
import * as storeApi from '@/store/api';
import EntitiesTab from '../Entities';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { EntityType } from '@/types';
import React from 'react';

// Mock the API
vi.mock('@/store/api', async () => {
  const actual = await vi.importActual('@/store/api');
  return {
    ...actual,
    useGetEntitiesQuery: vi.fn(),
    useCreateEntityMutation: vi.fn(),
    useUpdateEntityMutation: vi.fn(),
    useDeleteEntityMutation: vi.fn(),
  };
});

// Mock react-router-dom
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useSearchParams: () => [new URLSearchParams('?id=bot1'), vi.fn()],
  };
});

const mockEntities = [
  {
    id: '1',
    name: 'PersonName',
    type: EntityType.TEXT,
    botId: 'bot1',
    intentId: null,
    metadata: {},
    createdAt: '2023-01-01T00:00:00Z',
    updatedAt: '2023-01-01T00:00:00Z',
    createdBy: 'user1',
    updatedBy: 'user1',
  },
  {
    id: '2',
    name: 'EmailAddress',
    type: EntityType.EMAIL,
    botId: 'bot1',
    intentId: 'intent1',
    metadata: {},
    createdAt: '2023-01-01T00:00:00Z',
    updatedAt: '2023-01-01T00:00:00Z',
    createdBy: 'user1',
    updatedBy: 'user1',
  },
  {
    id: '3',
    name: 'PhonePattern',
    type: EntityType.REGEX,
    botId: 'bot1',
    intentId: null,
    metadata: { value: '^\\+?[1-9]\\d{1,14}$' },
    createdAt: '2023-01-01T00:00:00Z',
    updatedAt: '2023-01-01T00:00:00Z',
    createdBy: 'user1',
    updatedBy: 'user1',
  },
];

describe('EntitiesTab', () => {
  beforeEach(() => {
    vi.clearAllMocks();

    // Default successful API responses
    vi.mocked(storeApi.useGetEntitiesQuery).mockReturnValue({
      data: {
        data: {
          items: mockEntities,
          totalCount: 3,
          pagination: {
            page: 1,
            limit: 10,
            total: 3,
            totalPages: 1,
            hasNext: false,
            hasPrevious: false,
          },
        },
      },
      isLoading: false,
      isError: false,
      refetch: vi.fn(),
    } as any);

    const mockCreateEntity = vi.fn().mockResolvedValue({
      data: {
        id: 'new-entity',
        name: 'New Entity',
        type: EntityType.TEXT,
        botId: 'bot1',
        intentId: null,
        metadata: {},
      },
    });
    vi.mocked(storeApi.useCreateEntityMutation).mockReturnValue([
      vi.fn().mockImplementation(() => ({ unwrap: mockCreateEntity })),
      { isLoading: false, isError: false },
    ] as any);

    const mockUpdateEntity = vi.fn().mockResolvedValue({
      data: {
        id: '1',
        name: 'Updated Entity',
        type: EntityType.TEXT,
        botId: 'bot1',
        intentId: null,
        metadata: {},
      },
    });
    vi.mocked(storeApi.useUpdateEntityMutation).mockReturnValue([
      vi.fn().mockImplementation(() => ({ unwrap: mockUpdateEntity })),
      { isLoading: false, isError: false },
    ] as any);

    const mockDeleteEntity = vi.fn().mockResolvedValue({ data: { success: true } });
    vi.mocked(storeApi.useDeleteEntityMutation).mockReturnValue([
      vi.fn().mockImplementation(() => ({ unwrap: mockDeleteEntity })),
      { isLoading: false, isError: false },
    ] as any);
  });

  describe('Rendering and Basic Functionality', () => {
    it('renders entities tab with title and search functionality', () => {
      render(<EntitiesTab />);

      expect(screen.getByText('Entities')).toBeInTheDocument();
      expect(screen.getByPlaceholderText('Search')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /filter entities/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /add entity/i })).toBeInTheDocument();
    });

    it('displays entities in table format', () => {
      render(<EntitiesTab />);

      expect(screen.getByText('PersonName')).toBeInTheDocument();
      expect(screen.getByText('EmailAddress')).toBeInTheDocument();
      expect(screen.getByText('PhonePattern')).toBeInTheDocument();
      expect(screen.getByText('TEXT')).toBeInTheDocument();
      expect(screen.getByText('EMAIL')).toBeInTheDocument();
      expect(screen.getByText('REGEX')).toBeInTheDocument();
    });

    it('displays metadata values correctly', () => {
      render(<EntitiesTab />);

      // REGEX entity should show its pattern
      expect(screen.getByText('^\\+?[1-9]\\d{1,14}$')).toBeInTheDocument();

      // Non-REGEX entities should show dash
      const dashElements = screen.getAllByText('-');
      expect(dashElements).toHaveLength(2); // PersonName and EmailAddress
    });

    it('shows table headers correctly', () => {
      render(<EntitiesTab />);

      expect(screen.getByText('Name')).toBeInTheDocument();
      expect(screen.getByText('Type')).toBeInTheDocument();
      expect(screen.getByText('Value')).toBeInTheDocument();
      expect(screen.getByText('Action')).toBeInTheDocument();
    });
  });

  describe('Search Functionality', () => {
    it('filters entities by name', async () => {
      // Mock filtered results
      vi.mocked(storeApi.useGetEntitiesQuery).mockReturnValue({
        data: {
          data: {
            items: [mockEntities[0]], // Only PersonName
            totalCount: 1,
            pagination: {
              page: 1,
              limit: 10,
              total: 1,
              totalPages: 1,
              hasNext: false,
              hasPrevious: false,
            },
          },
        },
        isLoading: false,
        isError: false,
        refetch: vi.fn(),
      } as any);

      render(<EntitiesTab />);

      const searchInput = screen.getByPlaceholderText('Search');
      await userEvent.type(searchInput, 'Person');

      expect(screen.getByText('PersonName')).toBeInTheDocument();
      expect(screen.queryByText('EmailAddress')).not.toBeInTheDocument();
      expect(screen.queryByText('PhonePattern')).not.toBeInTheDocument();
    });

    it('filters entities case-insensitively', async () => {
      // Mock filtered results
      vi.mocked(storeApi.useGetEntitiesQuery).mockReturnValue({
        data: {
          data: {
            items: [mockEntities[1]], // Only EmailAddress
            totalCount: 1,
            pagination: {
              page: 1,
              limit: 10,
              total: 1,
              totalPages: 1,
              hasNext: false,
              hasPrevious: false,
            },
          },
        },
        isLoading: false,
        isError: false,
        refetch: vi.fn(),
      } as any);

      render(<EntitiesTab />);

      const searchInput = screen.getByPlaceholderText('Search');
      await userEvent.type(searchInput, 'email');

      expect(screen.getByText('EmailAddress')).toBeInTheDocument();
      expect(screen.queryByText('PersonName')).not.toBeInTheDocument();
      expect(screen.queryByText('PhonePattern')).not.toBeInTheDocument();
    });

    it('shows all entities when search is cleared', async () => {
      render(<EntitiesTab />);

      const searchInput = screen.getByPlaceholderText('Search');
      await userEvent.type(searchInput, 'Person');
      await userEvent.clear(searchInput);

      expect(screen.getByText('PersonName')).toBeInTheDocument();
      expect(screen.getByText('EmailAddress')).toBeInTheDocument();
      expect(screen.getByText('PhonePattern')).toBeInTheDocument();
    });

    it('shows no results when search matches nothing', async () => {
      // Mock empty results
      vi.mocked(storeApi.useGetEntitiesQuery).mockReturnValue({
        data: {
          data: {
            items: [],
            totalCount: 0,
            pagination: {
              page: 1,
              limit: 10,
              total: 0,
              totalPages: 0,
              hasNext: false,
              hasPrevious: false,
            },
          },
        },
        isLoading: false,
        isError: false,
        refetch: vi.fn(),
      } as any);

      render(<EntitiesTab />);

      const searchInput = screen.getByPlaceholderText('Search');
      await userEvent.type(searchInput, 'NonExistentEntity');

      expect(screen.queryByText('PersonName')).not.toBeInTheDocument();
      expect(screen.queryByText('EmailAddress')).not.toBeInTheDocument();
      expect(screen.queryByText('PhonePattern')).not.toBeInTheDocument();
    });
  });

  describe('Loading and Error States', () => {
    it('displays loading state', () => {
      vi.mocked(storeApi.useGetEntitiesQuery).mockReturnValue({
        data: undefined,
        isLoading: true,
        isError: false,
        refetch: vi.fn(),
      } as any);

      render(<EntitiesTab />);

      expect(screen.getByText('Loading...')).toBeInTheDocument();
    });

    it('displays error state', () => {
      vi.mocked(storeApi.useGetEntitiesQuery).mockReturnValue({
        data: undefined,
        isLoading: false,
        isError: true,
        error: { message: 'Failed to load entities' },
        refetch: vi.fn(),
      } as any);

      render(<EntitiesTab />);

      expect(screen.getByText('Error loading data')).toBeInTheDocument();
    });
  });

  describe('Empty State', () => {
    it('displays empty state when no entities exist', () => {
      vi.mocked(storeApi.useGetEntitiesQuery).mockReturnValue({
        data: {
          data: {
            items: [],
            totalCount: 0,
            pagination: {
              page: 1,
              limit: 10,
              total: 0,
              totalPages: 0,
              hasNext: false,
              hasPrevious: false,
            },
          },
        },
        isLoading: false,
        isError: false,
        refetch: vi.fn(),
      } as any);

      render(<EntitiesTab />);

      expect(screen.getByText('Start adding entities')).toBeInTheDocument();
      expect(screen.getByText('Nothing to show')).toBeInTheDocument();
      expect(screen.getAllByRole('button', { name: /add entity/i })).toHaveLength(2);
    });
  });

  describe('Entity Creation', () => {
    it('opens add entity modal when add button is clicked', async () => {
      render(<EntitiesTab />);

      const addButton = screen.getByRole('button', { name: /add entity/i });
      await userEvent.click(addButton);

      expect(screen.getByRole('heading', { name: 'ADD ENTITY' })).toBeInTheDocument();
    });

    it('creates a new TEXT entity successfully', async () => {
      render(<EntitiesTab />);

      const addButton = screen.getByRole('button', { name: /add entity/i });
      await userEvent.click(addButton);

      const nameInput = screen.getByLabelText(/entity name/i);
      await userEvent.type(nameInput, 'TestEntity');

      // The default type is TEXT, so we can submit directly
      const submitButton = screen.getByLabelText('Form Submit Button');
      await userEvent.click(submitButton);

      await waitFor(() => {
        expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
      });
    });

    it('shows form with entity name input and type selector', async () => {
      render(<EntitiesTab />);

      const addButton = screen.getByRole('button', { name: /add entity/i });
      await userEvent.click(addButton);

      // Check that form elements are present
      expect(screen.getByLabelText(/entity name/i)).toBeInTheDocument();
      expect(screen.getByRole('combobox', { name: /type/i })).toBeInTheDocument();
      expect(screen.getByLabelText('Form Submit Button')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: 'CANCEL' })).toBeInTheDocument();
    });

    it('validates required fields in entity form', async () => {
      render(<EntitiesTab />);

      const addButton = screen.getByRole('button', { name: /add entity/i });
      await userEvent.click(addButton);

      const submitButton = screen.getByLabelText('Form Submit Button');
      await userEvent.click(submitButton);

      // Form should still be open due to validation errors
      expect(screen.getByRole('heading', { name: 'ADD ENTITY' })).toBeInTheDocument();
    });

    it('validates required entity name field', async () => {
      render(<EntitiesTab />);

      const addButton = screen.getByRole('button', { name: /add entity/i });
      await userEvent.click(addButton);

      // Try to submit without filling name
      const submitButton = screen.getByLabelText('Form Submit Button');
      await userEvent.click(submitButton);

      // Form should still be open due to validation error
      expect(screen.getByRole('heading', { name: 'ADD ENTITY' })).toBeInTheDocument();
    });

    it('closes modal when cancel button is clicked', async () => {
      render(<EntitiesTab />);

      const addButton = screen.getByRole('button', { name: /add entity/i });
      await userEvent.click(addButton);

      const cancelButton = screen.getByRole('button', { name: 'CANCEL' });
      await userEvent.click(cancelButton);

      await waitFor(() => {
        expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
      });
    });

    it('handles entity creation failure gracefully', async () => {
      const mockCreateEntity = vi.fn().mockRejectedValue(new Error('Creation failed'));
      vi.mocked(storeApi.useCreateEntityMutation).mockReturnValue([
        vi.fn().mockImplementation(() => ({ unwrap: mockCreateEntity })),
        { isLoading: false, isError: true },
      ] as any);

      render(<EntitiesTab />);

      const addButton = screen.getByRole('button', { name: /add entity/i });
      await userEvent.click(addButton);

      const nameInput = screen.getByLabelText(/entity name/i);
      await userEvent.type(nameInput, 'TestEntity');

      const submitButton = screen.getByLabelText('Form Submit Button');
      await userEvent.click(submitButton);

      await waitFor(() => {
        expect(mockCreateEntity).toHaveBeenCalled();
      });
    });
  });

  describe('Entity Editing', () => {
    it('opens edit modal when edit button is clicked', async () => {
      render(<EntitiesTab />);

      const editButtons = screen.getAllByRole('button', { name: /edit/i });
      await userEvent.click(editButtons[0]);

      expect(screen.getByText('EDIT ENTITY')).toBeInTheDocument();
      expect(screen.getByDisplayValue('PersonName')).toBeInTheDocument();
    });

    it('updates entity successfully', async () => {
      render(<EntitiesTab />);

      const editButtons = screen.getAllByRole('button', { name: /edit/i });
      await userEvent.click(editButtons[0]);

      const nameInput = screen.getByDisplayValue('PersonName');
      await userEvent.clear(nameInput);
      await userEvent.type(nameInput, 'UpdatedPersonName');

      const submitButton = screen.getByLabelText('Form Submit Button');
      await userEvent.click(submitButton);

      await waitFor(() => {
        expect(screen.queryByText('EDIT ENTITY')).not.toBeInTheDocument();
      });
    });

    it('pre-fills form with existing entity data', async () => {
      render(<EntitiesTab />);

      const editButtons = screen.getAllByRole('button', { name: /edit/i });
      await userEvent.click(editButtons[2]); // PhonePattern entity

      expect(screen.getByDisplayValue('PhonePattern')).toBeInTheDocument();
      expect(screen.getByDisplayValue('^\\+?[1-9]\\d{1,14}$')).toBeInTheDocument();
    });

    it('handles entity update failure gracefully', async () => {
      const mockUpdateEntity = vi
        .fn()
        .mockRejectedValue({ data: { error: { message: 'Update failed' } } });
      vi.mocked(storeApi.useUpdateEntityMutation).mockReturnValue([
        vi.fn().mockImplementation(() => ({ unwrap: mockUpdateEntity })),
        { isLoading: false, isError: true },
      ] as any);

      render(<EntitiesTab />);

      const editButtons = screen.getAllByRole('button', { name: /edit/i });
      await userEvent.click(editButtons[0]);

      const nameInput = screen.getByDisplayValue('PersonName');
      await userEvent.clear(nameInput);
      await userEvent.type(nameInput, 'UpdatedPersonName');

      const submitButton = screen.getByLabelText('Form Submit Button');
      await userEvent.click(submitButton);

      await waitFor(() => {
        expect(mockUpdateEntity).toHaveBeenCalled();
        // Form should still be open due to error
        expect(screen.getByRole('heading', { name: 'EDIT ENTITY' })).toBeInTheDocument();
      });
    });
  });

  describe('Entity Deletion', () => {
    it('opens delete confirmation modal when delete button is clicked', async () => {
      render(<EntitiesTab />);

      const deleteButtons = screen.getAllByRole('button', { name: /delete/i });
      await userEvent.click(deleteButtons[0]);

      expect(screen.getByText('CONFIRM ENTITY DELETION')).toBeInTheDocument();
      expect(screen.getByText(/are you sure you want to delete this entity/i)).toBeInTheDocument();
    });

    it('deletes entity when confirmed', async () => {
      render(<EntitiesTab />);

      const deleteButtons = screen.getAllByRole('button', { name: /delete/i });
      await userEvent.click(deleteButtons[0]);

      const confirmButton = screen.getByRole('button', { name: 'YES, DELETE' });
      await userEvent.click(confirmButton);

      await waitFor(() => {
        expect(screen.queryByText('CONFIRM ENTITY DELETION')).not.toBeInTheDocument();
      });
    });

    it('cancels deletion when cancel button is clicked', async () => {
      render(<EntitiesTab />);

      const deleteButtons = screen.getAllByRole('button', { name: /delete/i });
      await userEvent.click(deleteButtons[0]);

      const cancelButton = screen.getByRole('button', { name: 'NO, CANCEL' });
      await userEvent.click(cancelButton);

      await waitFor(() => {
        expect(screen.queryByText('CONFIRM ENTITY DELETION')).not.toBeInTheDocument();
      });
    });

    it('handles entity deletion failure gracefully', async () => {
      const mockDeleteEntity = vi.fn().mockRejectedValue(new Error('Deletion failed'));
      vi.mocked(storeApi.useDeleteEntityMutation).mockReturnValue([
        vi.fn().mockImplementation(() => ({ unwrap: mockDeleteEntity })),
        { isLoading: false, isError: true },
      ] as any);

      render(<EntitiesTab />);

      const deleteButtons = screen.getAllByRole('button', { name: /delete/i });
      await userEvent.click(deleteButtons[0]);

      const confirmButton = screen.getByRole('button', { name: 'YES, DELETE' });
      await userEvent.click(confirmButton);

      await waitFor(() => {
        expect(mockDeleteEntity).toHaveBeenCalled();
      });
    });
  });

  describe('Table Styling and Layout', () => {
    it('applies correct styling to table rows', () => {
      render(<EntitiesTab />);

      const tableRows = screen.getAllByRole('row');
      // Skip header row
      const dataRows = tableRows.slice(1);

      // Check alternating row colors
      expect(dataRows[0]).toHaveClass('bg-tertiary-100');
      expect(dataRows[1]).not.toHaveClass('bg-tertiary-100');
      expect(dataRows[2]).toHaveClass('bg-tertiary-100');
    });

    it('displays action buttons correctly', () => {
      render(<EntitiesTab />);

      const editButtons = screen.getAllByRole('button', { name: /edit/i });
      const deleteButtons = screen.getAllByRole('button', { name: /delete/i });

      expect(editButtons).toHaveLength(3);
      expect(deleteButtons).toHaveLength(3);
    });

    it('handles different entity types correctly', () => {
      render(<EntitiesTab />);

      // Check that all entity types are displayed
      expect(screen.getByText('TEXT')).toBeInTheDocument();
      expect(screen.getByText('EMAIL')).toBeInTheDocument();
      expect(screen.getByText('REGEX')).toBeInTheDocument();
    });
  });

  describe('Form Validation Edge Cases', () => {
    it('validates entity name length', async () => {
      render(<EntitiesTab />);

      const addButton = screen.getByRole('button', { name: /add entity/i });
      await userEvent.click(addButton);

      const nameInput = screen.getByLabelText(/entity name/i);
      await userEvent.type(nameInput, 'a'.repeat(50)); // Exceeds max length

      const submitButton = screen.getByLabelText('Form Submit Button');
      await userEvent.click(submitButton);

      // Form should still be open due to validation error
      expect(screen.getByRole('heading', { name: 'ADD ENTITY' })).toBeInTheDocument();
    });

    it('shows form elements correctly', async () => {
      render(<EntitiesTab />);

      const addButton = screen.getByRole('button', { name: /add entity/i });
      await userEvent.click(addButton);

      // Initially no regex input should be visible (default is TEXT type)
      expect(screen.queryByLabelText(/regex value/i)).not.toBeInTheDocument();

      // Basic form elements should be present
      expect(screen.getByLabelText(/entity name/i)).toBeInTheDocument();
      expect(screen.getByRole('combobox', { name: /type/i })).toBeInTheDocument();
    });
  });

  describe('Integration with EntityHighlightInput', () => {
    it('provides entities for entity highlighting functionality', () => {
      render(<EntitiesTab />);

      // Verify that entities are loaded and available
      expect(screen.getByText('PersonName')).toBeInTheDocument();
      expect(screen.getByText('EmailAddress')).toBeInTheDocument();
      expect(screen.getByText('PhonePattern')).toBeInTheDocument();
    });
  });
});
