import { render, screen, waitFor } from '@/test/utils';
import TrainTabContent from '../index';
import { trainSubTabs } from '../config';
import { describe, expect, it, vi } from 'vitest';
import userEvent from '@testing-library/user-event';

vi.mock('../Entities', () => ({ default: () => <div>Entities Content</div> }));
vi.mock('../FAQs', () => ({ default: () => <div>FAQs Tab Content</div> }));
vi.mock('../IntentUtterances', () => ({ default: () => <div>Intent Utterances Tab Content</div> }));
vi.mock('../SmallTalk', () => ({ default: () => <div>Small Talk Content</div> }));
vi.mock('../Synonyms', () => ({ default: () => <div>Synonyms Content</div> }));
vi.mock('../TrainFromLogs', () => ({ default: () => <div>Train from Logs Content</div> }));

describe('TrainTabContent', () => {
  it('renders the tabs and defaults to Intent Utterances tab', () => {
    render(<TrainTabContent />);

    expect(screen.getByRole('tab', { name: 'Intent Utterances' })).toBeInTheDocument();
    expect(screen.getByRole('tab', { name: 'Entities' })).toBeInTheDocument();
    expect(screen.getByRole('tab', { name: 'FAQs' })).toBeInTheDocument();
    expect(screen.getByRole('tab', { name: 'Synonyms' })).toBeInTheDocument();
    expect(screen.getByRole('tab', { name: 'Small Talk' })).toBeInTheDocument();
    expect(screen.getByRole('tab', { name: 'Train from Logs' })).toBeInTheDocument();

    expect(screen.getByText('Intent Utterances Tab Content')).toBeInTheDocument();
    expect(screen.queryByText('FAQs Tab Content')).not.toBeInTheDocument();
  });

  it('switches to FAQs tab when clicked', async () => {
    render(<TrainTabContent />);

    await userEvent.click(screen.getByRole('tab', { name: 'FAQs' }));

    await waitFor(() => {
      expect(screen.getByText('FAQs Tab Content')).toBeInTheDocument();
      expect(screen.queryByText('Intent Utterances Tab Content')).not.toBeInTheDocument();
    });
  });

  it('switches to Entities tab when clicked', async () => {
    render(<TrainTabContent />);

    await userEvent.click(screen.getByRole('tab', { name: 'Entities' }));

    await waitFor(() => {
      expect(screen.getByText('Entities Content')).toBeInTheDocument();
    });
  });

  it('switches to Synonyms tab when clicked', async () => {
    render(<TrainTabContent />);

    await userEvent.click(screen.getByRole('tab', { name: 'Synonyms' }));

    await waitFor(() => {
      expect(screen.getByText('Synonyms Content')).toBeInTheDocument();
    });
  });

  it('switches to Small Talk tab when clicked', async () => {
    render(<TrainTabContent />);

    await userEvent.click(screen.getByRole('tab', { name: 'Small Talk' }));

    await waitFor(() => {
      expect(screen.getByText('Small Talk Content')).toBeInTheDocument();
    });
  });

  it('switches to Train from Logs tab when clicked', async () => {
    render(<TrainTabContent />);

    await userEvent.click(screen.getByRole('tab', { name: 'Train from Logs' }));

    await waitFor(() => {
      expect(screen.getByText('Train from Logs Content')).toBeInTheDocument();
    });
  });

  it('has correct tab configuration', () => {
    expect(trainSubTabs).toHaveLength(6);
    expect(trainSubTabs[0].id).toBe('Intent Utterances');
    expect(trainSubTabs[1].id).toBe('Entities');
    expect(trainSubTabs[2].id).toBe('FAQs');
    expect(trainSubTabs[3].id).toBe('Synonyms');
    expect(trainSubTabs[4].id).toBe('Small Talk');
    expect(trainSubTabs[5].id).toBe('Train from Logs');
  });

  it('maintains active tab state correctly', async () => {
    render(<TrainTabContent />);

    expect(screen.getByRole('tab', { name: 'Intent Utterances' })).toHaveAttribute(
      'aria-selected',
      'true'
    );
    expect(screen.getByRole('tab', { name: 'FAQs' })).toHaveAttribute('aria-selected', 'false');

    await userEvent.click(screen.getByRole('tab', { name: 'FAQs' }));

    await waitFor(() => {
      expect(screen.getByRole('tab', { name: 'FAQs' })).toHaveAttribute('aria-selected', 'true');
      expect(screen.getByRole('tab', { name: 'Intent Utterances' })).toHaveAttribute(
        'aria-selected',
        'false'
      );
    });
  });

  it('renders all tab components correctly', () => {
    render(<TrainTabContent />);

    expect(screen.getByRole('tab', { name: 'Intent Utterances' })).toBeInTheDocument();
    expect(screen.getByRole('tab', { name: 'Entities' })).toBeInTheDocument();
    expect(screen.getByRole('tab', { name: 'FAQs' })).toBeInTheDocument();
    expect(screen.getByRole('tab', { name: 'Synonyms' })).toBeInTheDocument();
    expect(screen.getByRole('tab', { name: 'Small Talk' })).toBeInTheDocument();
    expect(screen.getByRole('tab', { name: 'Train from Logs' })).toBeInTheDocument();
  });

  describe('Additional Coverage Tests', () => {
    it('applies correct CSS classes to active tab', async () => {
      render(<TrainTabContent />);

      const utterancesTab = screen.getByRole('tab', { name: 'Intent Utterances' });
      const entitiesTab = screen.getByRole('tab', { name: 'Entities' });

      expect(utterancesTab).toHaveClass('border-primary');
      expect(entitiesTab).not.toHaveClass('border-primary');

      await userEvent.click(entitiesTab);

      await waitFor(() => {
        expect(entitiesTab).toHaveClass('border-primary');
        expect(utterancesTab).not.toHaveClass('border-primary');
      });
    });

    it('applies correct content classes based on active tab', async () => {
      render(<TrainTabContent />);

      const entitiesTab = screen.getByRole('tab', { name: 'Entities' });
      await userEvent.click(entitiesTab);

      await waitFor(() => {
        const entitiesContent = screen.getByText('Entities Content');
        expect(entitiesContent).toBeInTheDocument();
      });
    });

    it('handles tab switching with keyboard navigation', async () => {
      render(<TrainTabContent />);

      const utterancesTab = screen.getByRole('tab', { name: 'Intent Utterances' });
      const entitiesTab = screen.getByRole('tab', { name: 'Entities' });

      utterancesTab.focus();
      expect(utterancesTab).toHaveFocus();

      await userEvent.keyboard('{ArrowRight}');

      expect(entitiesTab).toHaveFocus();
    });

    it('maintains tab state correctly during rapid switching', async () => {
      render(<TrainTabContent />);

      const utterancesTab = screen.getByRole('tab', { name: 'Intent Utterances' });
      const entitiesTab = screen.getByRole('tab', { name: 'Entities' });
      const faqsTab = screen.getByRole('tab', { name: 'FAQs' });

      await userEvent.click(entitiesTab);
      await userEvent.click(faqsTab);
      await userEvent.click(utterancesTab);

      await waitFor(() => {
        expect(screen.getByText('Intent Utterances Tab Content')).toBeInTheDocument();
        expect(screen.queryByText('Entities Content')).not.toBeInTheDocument();
        expect(screen.queryByText('FAQs Tab Content')).not.toBeInTheDocument();
      });
    });

    it('renders tab list with correct styling', () => {
      render(<TrainTabContent />);

      const tabList = screen.getByRole('tablist');
      expect(tabList).toHaveClass(
        'justify-start',
        'gap-3',
        'border-b',
        'px-6',
        'pb-0',
        'bg-tertiary-50',
        'pt-2'
      );
    });

    it('renders tab triggers with correct base styling', () => {
      render(<TrainTabContent />);

      const tabs = screen.getAllByRole('tab');
      tabs.forEach(tab => {
        expect(tab).toHaveClass('border-b-2', '!bg-transparent', 'rounded-none');
      });
    });

    it('handles tab content visibility correctly', async () => {
      render(<TrainTabContent />);

      expect(screen.getByText('Intent Utterances Tab Content')).toBeInTheDocument();
      expect(screen.queryByText('Entities Content')).not.toBeInTheDocument();

      await userEvent.click(screen.getByRole('tab', { name: 'Entities' }));

      await waitFor(() => {
        expect(screen.getByText('Entities Content')).toBeInTheDocument();
        expect(screen.queryByText('Intent Utterances Tab Content')).not.toBeInTheDocument();
      });
    });

    it('applies correct flex classes to tab content', async () => {
      render(<TrainTabContent />);

      const tabPanels = screen.getAllByRole('tabpanel');
      expect(tabPanels).toHaveLength(1);

      const activePanel = tabPanels[0];
      expect(activePanel).toHaveClass('flex', 'flex-col', 'h-0', '!m-0', 'pr-2', 'flex-1');
    });

    it('handles all tab configurations correctly', () => {
      render(<TrainTabContent />);

      expect(screen.getByRole('tab', { name: 'Intent Utterances' })).toBeInTheDocument();
      expect(screen.getByRole('tab', { name: 'Entities' })).toBeInTheDocument();
      expect(screen.getByRole('tab', { name: 'FAQs' })).toBeInTheDocument();
      expect(screen.getByRole('tab', { name: 'Synonyms' })).toBeInTheDocument();
      expect(screen.getByRole('tab', { name: 'Small Talk' })).toBeInTheDocument();
      expect(screen.getByRole('tab', { name: 'Train from Logs' })).toBeInTheDocument();
    });
  });
});
