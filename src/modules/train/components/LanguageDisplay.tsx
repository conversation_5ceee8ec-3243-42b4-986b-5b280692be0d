import React from 'react';
import { TooltipWrapper } from '@/components/TooltipWrapper';
import TagBox from '@/components/TagBox';
import { getTruncatedText } from '@/utils/uiUtil';

interface Language {
  id: string;
  name: string;
}

interface LanguageDisplayProps {
  languages?: Language[];
}

const LanguageDisplay: React.FC<LanguageDisplayProps> = ({ languages }) => {
  const displayedLanguages = languages?.slice(0, 3) || [];
  const hiddenLanguages = languages?.slice(3) || [];

  return (
    <div className="flex gap-2">
      {displayedLanguages.map(lang => (
        <TooltipWrapper content={lang.name} key={lang.id} hidden={lang.name.length <= 10}>
          <TagBox text={getTruncatedText(lang.name, 10)} />
        </TooltipWrapper>
      ))}
      {hiddenLanguages.length > 0 && (
        <TooltipWrapper content={hiddenLanguages.map(lang => lang.name).join(', ')}>
          <TagBox text={`+${hiddenLanguages.length}`} />
        </TooltipWrapper>
      )}
    </div>
  );
};

export default LanguageDisplay;