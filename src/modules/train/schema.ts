import * as z from 'zod';
import { TFunction } from 'i18next';
import { EntityType } from '@/types';

export const createAddQuestionFormSchema = (t: any) =>
  z.object({
    questions: z
      .array(
        z.object({
          value: z.string().trim().min(1, t('faqs.validation.questionRequired')),
        })
      )
      .min(1, t('faqs.validation.atLeastOneQuestion'))
      .refine(
        questions => {
          const values = questions.map(q => q.value.toLowerCase().trim());
          return values.length === new Set(values).size;
        },
        {
          message: t('faqs.validation.duplicateQuestions'),
        }
      ),
    answer: z
      .string()
      .transform(val => val.replace(/<[^>]*?>/g, '').trim()) // strip HTML
      .refine(val => val.length > 0, {
        message: t('faqs.validation.answerRequired'),
      }),

    langId: z.string().trim().min(1),
    translateTo: z.string().trim().optional(),
    flowId: z.string().trim().optional(),
  });

export type QuestionFormInputs = z.infer<ReturnType<typeof createAddQuestionFormSchema>>;

export const createAddIntentFormSchema = (t: TFunction) =>
  z.object({
    intentName: z
      .string()
      .trim()
      .min(1, { message: t('intents.nameRequired') })
      .max(32, t('validation.maxLength', { count: 32 })),
  });

export type AddIntentFormValues = z.infer<ReturnType<typeof createAddIntentFormSchema>>;

export const createAddUtteranceFormSchema = (t: TFunction) =>
  z.object({
    text: z.string().trim().min(1, t('intents.utterances.emptyError')),
    langId: z.string().trim(),
    translateTo: z.string().optional(),
  });

export type UtteranceForm = z.infer<ReturnType<typeof createAddUtteranceFormSchema>>;

export const createAddCategoryFormSchema = (t: TFunction) =>
  z.object({
    name: z
      .string()
      .trim()
      .min(1, t('faqs.category.nameRequired'))
      .max(32, t('validation.maxLength', { count: 32 })),
  });

export type CategoryForm = z.infer<ReturnType<typeof createAddCategoryFormSchema>>;

export const createEntitySchema = (t: TFunction) =>
  z
    .object({
      name: z
        .string()
        .trim()
        .min(1, t('entities.validation.nameRequired'))
        .max(32, t('validation.maxLength', { count: 32 })),
      type: z.nativeEnum(EntityType, {
        required_error: t('entities.validation.typeRequired'),
      }),
      metadata: z
        .object({
          value: z.string().trim().optional(),
        })
        .optional(),
    })
    .superRefine((data, ctx) => {
      if (data.type === EntityType.REGEX) {
        if (!data.metadata || !data.metadata.value) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            path: ['metadata.value'],
            message: t('entities.validation.valueRequired'),
          });
        }
      }
    });

export type EntityFormValues = z.infer<ReturnType<typeof createEntitySchema>>;
