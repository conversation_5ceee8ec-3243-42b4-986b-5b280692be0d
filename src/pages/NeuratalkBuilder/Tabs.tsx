import React, { useEffect } from 'react';
import { Tabs, TabsContent } from '../../components/ui/tabs';
import { neuraTalkBuilderTabs } from './config';
import { cn } from '@/lib/utils';
import TabBar from './TabBar';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { getNTBTab, RoutesName } from '@/lib/constant';
import { useBotTabIdsParam } from '@/hooks/useRouterParam';

import { Bot } from '@/types';
interface TabsComponentProps {
  bot: Bot | undefined;
}

function TabsComponent({ bot }: TabsComponentProps) {
  const { t } = useTranslation();
  const { botId, tabId } = useBotTabIdsParam();
  const navigate = useNavigate();

  useEffect(() => {
    if (!tabId) {
      //TODO: need to rm this useEffect
      // Redirect to the first tab if no tab is specified in the URL
      navigate(getNTBTab(botId, neuraTalkBuilderTabs[0].id), { replace: true });
    }
  }, [tabId, botId, navigate]);

  const handleTabChange = (value: string) => {
    navigate(getNTBTab(botId, value));
  };

  return (
    <Tabs value={tabId} onValueChange={handleTabChange} className="w-full h-0 flex-1 flex flex-col">
      <TabBar />

      {neuraTalkBuilderTabs.map(tab => (
        <TabsContent
          key={tab.id}
          value={tab.id}
          className={cn('!mt-0 border-none h-0 flex flex-col', {
            'flex-1': tabId === tab.id,
          })}
        >
          {tab.id === tabId && tab.Component ? (
            tab.id === 'Agent Transfer' ? (
              <tab.Component bot={bot} /> //TODO: pass it to all of the props
            ) : (
              <tab.Component />
            )
          ) : (
            <div className="flex items-center justify-center p-6 bg-muted h-full text-secondary-500">
              <p>{t('tabs.contentComingSoon', { tabName: tab.labelKey })}</p>
            </div>
          )}
        </TabsContent>
      ))}
    </Tabs>
  );
}

export default TabsComponent;
