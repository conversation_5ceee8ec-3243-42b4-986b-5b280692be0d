import React, { useState, FC } from 'react';
import { useNavigate } from 'react-router-dom';
import { Pencil } from 'lucide-react';
import Group_24426 from '@/assets/icons/Group_24426.svg';
import { useUpdateBotMutation } from '@/store/api/chatBotApi';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import {
  Dialog,
  DialogTrigger,
  DialogContent,
  DialogHeader,
  DialogFooter,
  DialogTitle,
  DialogClose,
} from '@/components/ui/dialog';
import { useTranslation } from 'react-i18next';
import { useToast } from '@/hooks/use-toast';
import { RoutesName } from '@/lib/constant';
import { ApiResponse, Bot, DomainOption, TranslationFunction } from '@/types';
import { useForm } from 'react-hook-form';
import { Form } from '@/components/ui/form';
import { zodResolver } from '@hookform/resolvers/zod';
import { createEditFormSchema, EditFormInputs } from './schema';
import EditForm from './editFormInput';
import { useBotIdParam } from '@/hooks/useRouterParam';

interface HeaderProps {
  // Define HeaderProps here
  bot: ApiResponse<Bot>;
  isLoading: boolean;
  error?: any;
}

const DOMAIN_OPTIONS_CONFIG = [
  { value: 'IT', labelKey: 'domains.it' },
  { value: 'Ecomm', labelKey: 'domains.ecomm' },
  { value: 'Telecom', labelKey: 'domains.telecom' },
  { value: 'Retail', labelKey: 'domains.retail' },
  { value: 'Travel', labelKey: 'domains.travel' },
  { value: 'Other', labelKey: 'domains.other' },
];

const getAvatarUrlFromName = (name: string): string => {
  const words = name.trim().split(' ');
  if (words.length === 0) return '';
  const initials = words[0][0] + (words.length > 1 ? words[words.length - 1][0] : '');
  return `https://ui-avatars.com/api/?name=${initials}&rounded=true&background=a0a0a0`; //TODO: remove this with image original api when available
};

const BotAvatar: FC<{ botName: string | undefined }> = ({ botName }) => (
  <div className="w-9 h-9 p-1 bg-tertiary-200 rounded-full flex items-center justify-center">
    <img
      src={botName ? getAvatarUrlFromName(botName) : Group_24426}
      alt="Chatbot Icon"
      className="w-full h-full object-contain"
      data-testid="chatbot-image"
    />
  </div>
);

const BotInfo: FC<{
  bot: ApiResponse<Bot>;
  t: TranslationFunction;
  onEditClick: () => void;
}> = ({ bot, t, onEditClick }) => (
  <div className="flex-1">
    <div className="flex items-center gap-2">
      <h3 className="text-sm font-medium text-tertiary-900" data-testid="chatbot-name">
        {bot?.data?.name || t('chatbot.untitled')}
      </h3>
      <DialogTrigger asChild>
        <button
          type="button"
          onClick={onEditClick}
          aria-label="Edit Chatbot"
          className="p-1 hover:bg-tertiary-100 rounded"
          data-testid="edit-button"
        >
          <Pencil className="w-3.5 h-3.5 text-tertiary-600" />
        </button>
      </DialogTrigger>
    </div>
    <div className="text-xs font-medium text-tertiary-500 mt-0.5" data-testid="chatbot-domain">
      {bot?.data?.domain ?? t('chatbot.noDomain')}
    </div>
    <div className="text-xs text-tertiary-500 mt-1" data-testid="chatbot-description">
      {bot?.data?.description || t('chatbot.noDescription')}
    </div>
  </div>
);

const Header: FC<HeaderProps> = ({ bot, isLoading, error }) => {
  const { t } = useTranslation();
  const { toast } = useToast();
  const { botId } = useBotIdParam();
  const navigate = useNavigate();

  const [updateBot, { isLoading: isUpdating }] = useUpdateBotMutation();
  const [dialogOpen, setDialogOpen] = useState<boolean>(false);

  const DOMAIN_OPTIONS: DomainOption[] = DOMAIN_OPTIONS_CONFIG.map(opt => ({
    value: opt.value,
    label: t(opt.labelKey),
  }));

  const methods = useForm<EditFormInputs>({
    resolver: zodResolver(createEditFormSchema(t)),
    mode: 'onChange',
    defaultValues: {
      name: '',
      domain: '',
      description: '',
    },
  });

  const {
    formState: { isValid, isDirty },
  } = methods;

  const handleEditClick = () => {
    if (bot) {
      methods.reset({
        name: bot?.data?.name || '',
        domain: bot?.data?.domain || '',
        description: bot?.data?.description || '',
      });
    }
    setDialogOpen(true);
  };

  const handleSaveForm = async (data: EditFormInputs) => {
    if (!botId) return;
    try {
      const payload = {
        name: data.name.trim(),
        domain: data.domain,
        description: data.description?.trim() || 'No description',
      };
      await updateBot({ id: botId, payload }).unwrap();
      toast({ title: t('editor.updateSuccess'), variant: 'default' });
      setDialogOpen(false);
    } catch (err: any) {
      console.error('Update failed:', err);
      toast({
        title: t('editor.updateError'),
        description: err,
        variant: 'destructive',
      });
    }
  };

  if (isLoading) {
    return (
      <div data-testid="loading" className="text-tertiary-600">
        {t('common.loading')}
      </div>
    );
  }
  if (error) {
    return (
      <div data-testid="error" className="text-error-500">
        {t('common.error')}
      </div>
    );
  }

  return (
    <div className="bg-white" data-testid="header-container">
      <div className="px-6 py-3 flex justify-between items-center">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink
                onClick={() => navigate(RoutesName.HOME)}
                className="hover:underline"
                data-testid="breadcrumb-neuraTalk"
              >
                {t('navigation.neuraTalk')}
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage data-testid="breadcrumb-create">
                {t('navigation.create')}
              </BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
      </div>
      <div className="px-6 py-4 border-tertiary-200">
        <div className="flex items-start gap-3">
          <BotAvatar botName={bot?.data?.name} />
          <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
            <BotInfo bot={bot} t={t} onEditClick={handleEditClick} />
            <Form {...methods}>
              <DialogContent
                className="fixed left-1/2 top-1/2 z-50 w-full max-w-[418px] -translate-x-1/2 -translate-y-1/2 rounded-xl bg-white shadow-lg px-6"
                data-testid="edit-dialog"
              >
                <form onSubmit={methods.handleSubmit(handleSaveForm)}>
                  <DialogHeader className="space-y-2">
                    <DialogTitle
                      className="font-normal text-sm uppercase"
                      data-testid="dialog-title"
                    >
                      {t('common.edit')}
                    </DialogTitle>
                  </DialogHeader>
                  <hr className="border-tertiary-200 mt-3 my-1 -mx-6" />
                  <div className="flex flex-col gap-4 my-6">
                    <EditForm DOMAIN_OPTIONS={DOMAIN_OPTIONS} t={t} />
                  </div>
                  <DialogFooter>
                    <DialogClose asChild>
                      <button
                        type="button"
                        onClick={() => setDialogOpen(false)}
                        className="px-6 py-2 w-28 rounded border text-tertiary-700 bg-background text-sm hover:bg-tertiary-100"
                        data-testid="cancel-button"
                      >
                        {t('common.cancel')}
                      </button>
                    </DialogClose>
                    <button
                      type="submit"
                      disabled={!isValid || !isDirty || isUpdating}
                      className="px-6 py-2 w-28 rounded bg-primary-500 text-white text-sm disabled:opacity-50"
                      data-testid="save-button"
                    >
                      {t('common.save')}
                    </button>
                  </DialogFooter>
                </form>
              </DialogContent>
            </Form>
          </Dialog>
        </div>
      </div>
    </div>
  );
};

export default Header;
