import { TFunction } from 'i18next';
import z from 'zod';

export const NAME_REGEX = /^[a-zA-Z0-9._-]+$/;
export const MAX_NAME_LENGTH = 50;
export const MAX_DESC_LENGTH = 150;
export const MAX_FILE_SIZE = 2 * 1024 * 1024;

export const createEditFormSchema = (t: TFunction) =>
  z.object({
    name: z
      .string().trim()
      .min(1, t('editor.nameRequired'))
      .max(MAX_NAME_LENGTH, t('editor.nameMaxError'))
      .regex(NAME_REGEX, t('editor.invalidName')),
    domain: z.string().trim().min(1, t('editor.domainRequired')),
    description: z
      .string().trim()
      .max(MAX_DESC_LENGTH, t('editor.descMaxError'))
      .min(1, t('editor.descRequired')),
    image: z.string().optional(),
  });

export type EditFormInputs = z.infer<ReturnType<typeof createEditFormSchema>>;
