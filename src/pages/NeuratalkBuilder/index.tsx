import React, { useEffect, useState } from 'react';
import PreviewModal from '@/modules/Preview/preview-modal';
import Header from './header';
import TabsComponent from './Tabs';
import { useGetBotByIdQuery } from '@/store/api/chatBotApi';
import { useBotIdParam } from '@/hooks/useRouterParam';
import MicroFrontendWrapper from '@/components/MicroFrontendWrapper';
import EditorLoader from '@/components/editor-loader';
function NeuraTalkBuilderContent() {
  const [isLoaderVisible, setIsLoaderVisible] = useState(true);
  const { botId } = useBotIdParam();
  const { data: bot, isLoading, error } = useGetBotByIdQuery({ id: botId }, { skip: !botId });

  useEffect(() => {
    setTimeout(() => {
      setIsLoaderVisible(false);
    }, 1000);
  }, []);

  if (isLoaderVisible || isLoading) {
    return <EditorLoader />;
  }
  return (
    <div className="h-screen">
      <div className="flex bg-background h-full">
        <div className="flex-1 flex flex-col h-full overflow-hidden w-0">
          <Header bot={bot!} isLoading={isLoading} error={error} />
          <TabsComponent bot={bot} />
        </div>
        <PreviewModal />
      </div>
    </div>
  );
}

export default function NeuraTalkBuilder() {
  return (
    <MicroFrontendWrapper>
      <NeuraTalkBuilderContent />
    </MicroFrontendWrapper>
  );
}
