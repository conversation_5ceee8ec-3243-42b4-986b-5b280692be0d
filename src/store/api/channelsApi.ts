import {
  ApiResponse,
  PaginatedResponse,
  PaginationParams,
  Channel,
} from '@/types';
import { apiSlice } from '../apiSlice';
import { serializePaginationParams } from '../../lib/utils/api';

const updatedApiSlice = apiSlice.enhanceEndpoints({
  addTagTypes: ['Channel'],
});

export const channelsApiSlice = updatedApiSlice.injectEndpoints({
  endpoints: builder => ({
    getChannels: builder.query<ApiResponse<PaginatedResponse<Channel>>, PaginationParams>({
      query: (params = { page: 1, limit: 20, search: '' }) => {
        const { searchParams } = serializePaginationParams(params);
        return {
          url: `/available-channels?${searchParams}`,
          method: 'GET',
        };
      },
      providesTags: ['Channel'],
    }),
    getBotChannelDetails: builder.query<ApiResponse<Channel>, { botId: string; channelId: string }>(
      {
        query: ({ botId, channelId }) => ({
          url: `/channels/${channelId}/bots/${botId}`,
          method: 'GET',
        }),
        providesTags: ['Channel'],
      }
    ),
    getAllBotChannels: builder.query<ApiResponse, { botId: string } & PaginationParams>({
      query: ({ botId, ...params }) => {
        const { searchParams } = serializePaginationParams(params);
        return {
          url: `/channels/bots/${botId}?${searchParams}`,
          method: 'GET',
        };
      },
      providesTags: ['Channel'],
    }),
  }),
});

export const { useGetChannelsQuery, useGetBotChannelDetailsQuery, useGetAllBotChannelsQuery } =
  channelsApiSlice;
