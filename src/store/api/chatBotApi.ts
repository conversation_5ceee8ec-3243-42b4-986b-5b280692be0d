import {
  ApiResponse,
  Bot,
  PaginatedResponse,
  PaginationParams,
  FlowVersion,
  Application,
} from '@/types';
import { apiSlice } from '../apiSlice';
import { serializePaginationParams } from '../../lib/utils/api';
import { createEntityMixins } from '@/lib/utils/optimizedTags';
export interface UpdateBotRequest {
  name: string;
  domain: string;
  description: string;
}

const updatedApiSlice = apiSlice.enhanceEndpoints({
  addTagTypes: ['Bot'],
});
const botMixins = createEntityMixins<Bot, 'Bot', typeof updatedApiSlice, PaginationParams>({
  entityType: 'Bot',
  apiInstance: updatedApiSlice,
  getSerializeQueryArgs: args => {
    const { queryArgs } = args;
    const { page, ...param } = queryArgs;
    return `bot_${JSON.stringify(param)}`;
  },
  options: {
    itemEndpointName: 'getFlowById',
  },
});
export const chatbotApiSlice = updatedApiSlice.injectEndpoints({
  endpoints: builder => ({
    getBots: builder.query<ApiResponse<PaginatedResponse<Bot>>, PaginationParams>({
      query: ({ ...params }) => {
        const { searchParams } = serializePaginationParams(params);

        return {
          url: `/bots?${searchParams}`,
          method: 'GET',
        };
      },
      ...botMixins.paginated,
      providesTags: ['Bot'],
    }),

    getBotById: builder.query<Bot, { id: string }>({
      query: ({ id }) => {
        return {
          url: `/bots/${id}`,
          method: 'GET',
        };
      },
      ...botMixins.item,
    }),
    createBot: builder.mutation<
      ApiResponse<Bot>,
      { title: string; domain: string; description: string }
    >({
      query: ({ title, domain, description }) => {
        return {
          url: '/bots',
          method: 'POST',
          body: { name: title, domain, description },
        };
      },
      ...botMixins.create(),
    }),
    buildBot: builder.mutation<Bot, { botId: string }>({
      query: ({ botId }) => ({
        url: `/bots/${botId}/build`,
        method: 'POST',
      }),
    }),
    deleteBot: builder.mutation<void, { id: string }>({
      query: ({ id }) => {
        return {
          url: `/bots/${id}`,
          method: 'DELETE',
        };
      },
      ...botMixins.delete(),
    }),
    updateBot: builder.mutation<Bot, { id: string; payload: UpdateBotRequest }>({
      query: ({ id, payload }) => ({
        url: `/bots/${id}`,
        method: 'PUT',
        body: payload,
      }),
      ...botMixins.update(),
    }),
    cloneBot: builder.mutation<Bot, { botId: string }>({
      query: ({ botId }) => ({
        url: `/bots/${botId}/clone`,
        method: 'POST',
      }),
      transformResponse: (response: { data: Bot }) => response.data,
      invalidatesTags: ['Bot'],
    }),
    exportBot: builder.mutation<Bot, { botId: string }>({
      query: ({ botId }) => ({
        url: `/bots/${botId}/export`,
        method: 'GET',
      }),
      transformResponse: (response: { data: Bot }) => response.data,
    }),
    publishBot: builder.mutation<void, { botId: string }>({
      query: ({ botId }) => ({
        url: `/bots/${botId}/publish`,
        method: 'POST',
      }),
      invalidatesTags: ['Bot'],
    }),
    getFlowVersions: builder.query<ApiResponse<FlowVersion[]>, { appId: string }>({
      query: ({ appId }) => ({
        url: `/apps/${appId}/versions`,
        method: 'GET',
      }),
    }),
    getFlowVersionData: builder.query<ApiResponse<Application>, { appId: string; version: string }>({
      query: ({ appId, version }) => ({
        url: `/apps/${appId}/${version}`,
        method: 'GET',
      }),
    }),
    addChannel: builder.mutation<ApiResponse<Bot>, { botId: string; channelId: string, clientId: string }>({
      query: ({ botId, channelId, clientId }) => ({
        url: `/bots/${botId}/channels`,
        method: 'POST',
        body: { channelId, clientId },
      }),
      invalidatesTags: ['Bot'],
    }),
    getChannelDetails: builder.query<ApiResponse<any>, { botId: string }>({
      query: ({ botId }) => ({
        url: `/bots/${botId}/live-agent-channel`,
        method: 'GET',
      }),
    }),
  }),
});

export const {
  useGetBotsQuery,
  useGetBotByIdQuery,
  useBuildBotMutation,
  useCreateBotMutation,
  useDeleteBotMutation,
  useUpdateBotMutation,
  useCloneBotMutation,
  useExportBotMutation,
  usePublishBotMutation,
  useGetFlowVersionsQuery,
  useGetFlowVersionDataQuery,
  useAddChannelMutation,
  useGetChannelDetailsQuery,
} = chatbotApiSlice;
