import {
  ApiResponse,
  CreateFaqCategoryRequest,
  CreateFaqTranslationRequest,
  FaqCategory,
  FaqItem,
  FaqTranslation,
  FaqTranslationByLangParam,
  GetFaqsByCategoryAndLanguageParams,
  PaginatedResponse,
  PaginationParams,
  UpdateFaqCategoryRequest,
  UpdateFaqTranslationRequest,
  UuidParams,
  CreateFaqResponseData,
  UpdateFaqResponseData,
} from '@/types';
import { apiSlice } from '../apiSlice';
import { serializePaginationParams } from '../../lib/utils/api';

import { createEntityMixins } from '../../lib/utils/optimizedTags';

const updatedApiSlice = apiSlice.enhanceEndpoints({
  addTagTypes: ['FaqCategory', 'FaqTranslation'],
});

//TODO: need to implement infinite scrolling for faqtranslation
// FaqTranslation configuration
const faqTranslationMixins = createEntityMixins<
  FaqItem,
  'FaqTranslation',
  typeof updatedApiSlice,
  GetFaqsByCategoryAndLanguageParams
>({
  entityType: 'FaqTranslation',
  apiInstance: updatedApiSlice,
  getSerializeQueryArgs: ({ queryArgs, endpointName }) => {
    if (endpointName === 'getTranslationByFaqIdAndLangId') {
      const { faqId } = queryArgs as FaqTranslationByLangParam;
      return `faq-translation_${faqId}`;
    }
    const { categoryId, langId } = queryArgs as GetFaqsByCategoryAndLanguageParams;
    return `faq-translations_${categoryId}_${langId}`;
  },
  paginationMerge: (currentCache, newItems) => {
    // Initialize currentCache if it's null or undefined
    if (!currentCache) {
      currentCache = {
        success: true,
        timestamp: new Date().toISOString(),
        data: {
          items: [],
          pagination: {
            page: 0,
            limit: 0,
            total: 0,
            totalPages: 0,
            hasNext: false,
            hasPrev: false,
          },
        },
      };
    }

    // Ensure currentCache.data and currentCache.data.items exist
    if (!currentCache.data) {
      currentCache.data = {
        items: [],
        pagination: { page: 0, limit: 0, total: 0, totalPages: 0, hasNext: false, hasPrev: false },
      };
    }
    if (!currentCache.data.items) {
      currentCache.data.items = [];
    }

    // Handle paginated query response
    if (newItems.data && Array.isArray(newItems.data.items)) {
      currentCache.data.items.push(...newItems.data.items);
      currentCache.data.pagination = newItems.data.pagination;
    }
    // Handle mutation response (single item)
    else if (newItems.data) {
      let newFaqItem: FaqItem | undefined;

      // Check if it's a CreateFaqResponseData structure
      if (
        'translation' in newItems.data &&
        typeof newItems.data.translation === 'object' &&
        newItems.data.translation !== null &&
        'id' in newItems.data.translation
      ) {
        const createResponse = newItems.data as unknown as CreateFaqResponseData;
        const translation = createResponse.translation;
        newFaqItem = {
          id: createResponse.id,
          botId: createResponse.botId,
          categoryId: createResponse.categoryId,
          questions: translation.questions,
          answer: translation.answer,
          langId: translation.langId,
          faqId: translation.faqId,
          createdAt: createResponse.createdAt,
          updatedAt: createResponse.updatedAt,
          createdBy: createResponse.createdBy,
          updatedBy: createResponse.updatedBy,
        };
      }
      // Check if it's an UpdateFaqResponseData structure
      else if (
        'faqItem' in newItems.data &&
        typeof newItems.data.faqItem === 'object' &&
        newItems.data.faqItem !== null &&
        'id' in newItems.data.faqItem
      ) {
        const updateResponse = newItems.data as unknown as UpdateFaqResponseData;
        const faqItemFromResponse = updateResponse.faqItem;
        newFaqItem = {
          ...faqItemFromResponse,
          questions: updateResponse.questions,
          answer: updateResponse.answer,
          langId: updateResponse.langId,
          faqId: updateResponse.faqId,
          updatedAt: updateResponse.updatedAt,
          updatedBy: updateResponse.updatedBy,
        };
      }

      if (newFaqItem) {
        const existingIndex = currentCache.data.items.findIndex(
          (item: any) => item.id === newFaqItem.id
        );

        if (existingIndex !== -1) {
          (currentCache.data.items as any)[existingIndex] = newFaqItem;
        } else {
          (currentCache.data.items as any).push(newFaqItem);
        }
      }
    }
    return currentCache;
  },
  options: {
    listEndpointName: 'getFaqsByCategoryAndLanguage',
    itemEndpointName: 'getTranslationByFaqIdAndLangId',
  },
  shouldUpdateCache: (queryArgs, newEntity) => {
    return queryArgs.categoryId === newEntity.categoryId && queryArgs.langId === newEntity.langId;
  },
  getItemQueryArgs: entity => ({
    faqId: entity.faqId,
    langId: entity.langId,
    categoryId: entity.categoryId,
  }),
});

// FaqCategory configuration
const faqCategoryMixins = createEntityMixins<
  FaqCategory,
  'FaqCategory',
  typeof updatedApiSlice,
  PaginationParams
>({
  entityType: 'FaqCategory',
  apiInstance: updatedApiSlice,
  getSerializeQueryArgs: ({ queryArgs }) => {
    const {
      filter: { botId },
    } = queryArgs;

    return `faq-categories_${JSON.stringify(botId)}`;
  },
  options: {
    listEndpointName: 'getFaqCategories',
  },
});

export const faqApi = updatedApiSlice.injectEndpoints({
  endpoints: builder => ({
    // FAQ Categories
    getFaqCategories: builder.query<ApiResponse<PaginatedResponse<FaqCategory>>, PaginationParams>({
      query: params => {
        const { searchParams } = serializePaginationParams(params);
        return {
          url: `/faq-categories?${searchParams}`,
        };
      },
      ...faqCategoryMixins.paginated,
    }),
    getFaqCategory: builder.query<ApiResponse<FaqCategory>, UuidParams>({
      query: ({ id }) => ({ url: `/faq-categories/${id}` }),
      ...faqCategoryMixins.item,
    }),
    createFaqCategory: builder.mutation<ApiResponse<FaqCategory>, CreateFaqCategoryRequest>({
      query: body => ({
        url: '/faq-categories',
        method: 'POST',
        body,
      }),
      ...faqCategoryMixins.create(),
    }),
    updateFaqCategory: builder.mutation<
      ApiResponse<FaqCategory>,
      UuidParams & UpdateFaqCategoryRequest
    >({
      query: ({ id, ...body }) => ({
        url: `/faq-categories/${id}`,
        method: 'PUT',
        body,
      }),
      ...faqCategoryMixins.update(),
    }),
    deleteFaqCategory: builder.mutation<void, UuidParams>({
      query: ({ id }) => ({
        url: `/faq-categories/${id}`,
        method: 'DELETE',
      }),
      ...faqCategoryMixins.delete(),
    }),

    getFaqTranslationsCount: builder.query<ApiResponse<number>, { categoryId: string }>({ 
      query: ({ categoryId }) => ({ 
        url: `/faq-translations/count/category/${categoryId}`, 
        method: 'GET', 
      }), 
    }),

    // FAQ Translations
    getFaqsByCategoryAndLanguage: builder.query<
      ApiResponse<PaginatedResponse<FaqItem>>,
      GetFaqsByCategoryAndLanguageParams
    >({
      query: ({ categoryId, langId, ...params }) => {
        const { searchParams } = serializePaginationParams(params);
        return {
          url: `/faqs/category/${categoryId}/language/${langId}?${searchParams}`,
        };
      },
      // ...faqTranslationMixins.paginated,
      providesTags: ['FaqTranslation'],
    }),
    getTranslationByFaqIdAndLangId: builder.query<
      ApiResponse<FaqTranslation>,
      FaqTranslationByLangParam
    >({
      query: ({ faqId, langId }) => ({
        url: `/faq/${faqId}/lang/${langId}/translation`,
      }),
      // ...faqTranslationMixins.item,
      providesTags: (_result, _error, { faqId, langId }) => [
        { type: 'FaqTranslation', id: `${faqId}-${langId}` },
      ],
    }),
    createFaqTranslation: builder.mutation<
      ApiResponse<CreateFaqResponseData>,
      CreateFaqTranslationRequest
    >({
      query: body => ({
        url: '/faq-translations',
        method: 'POST',
        body,
      }),
      // ...faqTranslationMixins.create,
      invalidatesTags: ['FaqTranslation'],
    }),
    updateFaqTranslation: builder.mutation<
      ApiResponse<UpdateFaqResponseData>,
      UuidParams & UpdateFaqTranslationRequest
    >({
      query: ({ id, ...body }) => ({
        url: `/faq-translations/${id}`,
        method: 'PUT',
        body,
      }),
      // ...faqTranslationMixins.update,
      invalidatesTags: ['FaqTranslation'],
    }),
    deleteFaqTranslation: builder.mutation<void, UuidParams>({
      query: ({ id }) => ({
        url: `/faq-translations/${id}`,
        method: 'DELETE',
      }),
      // ...faqTranslationMixins.delete,
      invalidatesTags: ['FaqTranslation'],
    }),
  }),
});

export const {
  useGetFaqCategoriesQuery,
  useGetFaqCategoryQuery,
  useCreateFaqCategoryMutation,
  useUpdateFaqCategoryMutation,
  useDeleteFaqCategoryMutation,
  useGetFaqsByCategoryAndLanguageQuery,

  useCreateFaqTranslationMutation,
  useUpdateFaqTranslationMutation,
  useDeleteFaqTranslationMutation,
  useGetTranslationByFaqIdAndLangIdQuery,
  useGetFaqTranslationsCountQuery,
} = faqApi;
