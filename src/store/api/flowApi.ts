import { serializePaginationParams } from '@/lib/utils/api';
import { apiSlice } from '../apiSlice';
import {
  ApiResponse,
  CreateSingleFlowPayload,
  FlowNode,
  PaginatedResponse,
  PaginationParams,
} from '@/types';
import { createEntityMixins } from '@/lib/utils/optimizedTags';

const updatedApiSlice = apiSlice.enhanceEndpoints({
  addTagTypes: ['Flow'],
});

// Flow configuration
const flowMixins = createEntityMixins<FlowNode, 'Flow', typeof updatedApiSlice, PaginationParams>({
  entityType: 'Flow',
  apiInstance: updatedApiSlice,
  getSerializeQueryArgs: args => {
    const { queryArgs } = args;
    const { botId, page, ...param } = queryArgs;
    return `flows_${botId}_${JSON.stringify(param)}`;
  },
  options: {
    itemEndpointName: 'getFlowById',
  },
});

// --- API Slice ---
export const flowApiSlice = updatedApiSlice.injectEndpoints({
  endpoints: builder => ({
    // GET all flows for a bot with pagination
    getFlows: builder.query<
      ApiResponse<PaginatedResponse<FlowNode>>,
      PaginationParams & { botId: string }
    >({
      query: ({ botId, ...params }) => {
        const { searchParams } = serializePaginationParams(params);
        return {
          url: `bots/${botId}/flows?${searchParams}`,
        };
      },
      ...flowMixins.paginated,
    }),

    // GET single flow by ID
    getFlowById: builder.query<ApiResponse<FlowNode>, { appId: string; id: string }>({
      query: ({ id }) => ({
        url: `/flows/${id}`,
      }),
      ...flowMixins.item,
    }),

    // POST - Create a new flow
    createFlow: builder.mutation<ApiResponse<FlowNode>, CreateSingleFlowPayload>({
      query: payload => ({
        url: `/flows`,
        method: 'POST',
        body: payload,
      }),
      ...flowMixins.create(),
    }),

    // PUT - Update an existing flow
    updateFlow: builder.mutation<
      ApiResponse<FlowNode>,
      { appId: string; id: string; payload: Partial<CreateSingleFlowPayload> }
    >({
      query: ({ id, payload }) => ({
        url: `/flows/${id}`, //TODO: change id to flowId to match with optimised tag
        method: 'PUT',
        body: payload,
      }),
      ...flowMixins.update(),
    }),

    // DELETE - Delete a flow
    deleteFlow: builder.mutation<void, { id: string; appId: string }>({
      query: ({ id, appId }) => ({
        url: `/flows/${id}/apps/${appId}`, //TODO: change id to flowId to match with optimised tag
        method: 'DELETE',
      }),
      ...flowMixins.delete(),
    }),

    // POST - Clone a flow
    cloneFlow: builder.mutation<FlowNode, { flowId: string }>({
      query: ({ flowId }) => ({
        url: `/flows/${flowId}/clone`,
        method: 'POST',
      }),
      transformResponse: (response: { data: FlowNode }) => response.data,
      invalidatesTags: ['Flow'],
    }),
  }),
});

// --- Export Hooks ---
export const {
  useGetFlowsQuery,
  useGetFlowByIdQuery,
  useCreateFlowMutation,
  useUpdateFlowMutation,
  useDeleteFlowMutation,
  useCloneFlowMutation,
} = flowApiSlice;
