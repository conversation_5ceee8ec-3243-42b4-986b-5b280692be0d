import { ApiResponse } from '@/types';
import { apiSlice } from '../apiSlice';
import { PlatformConfig } from '@/types/platformConfig.type';

export const platformConfigApi = apiSlice.injectEndpoints({
  endpoints: builder => ({
    getPlatformConfigByKey: builder.query<ApiResponse<PlatformConfig>, string>({
      query: key => {
        return {
          url: `/platform-configs/${key}`,
          method: 'GET',
        };
      },
    }),
  }),
});

export const { useGetPlatformConfigByKeyQuery } = platformConfigApi;
