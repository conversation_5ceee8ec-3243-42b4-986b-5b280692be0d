import {
  ApiResponse,
  PaginatedResponse,
  PaginationParams,
  WhatsappProfile,
  CreateWhatsappProfileRequest,
  ConnectWABANumberToBotRequest,
} from '@/types';
import { apiSlice } from '../apiSlice';

const updatedApiSlice = apiSlice.enhanceEndpoints({
  addTagTypes: ['WhatsappProfile'],
});

export const whatsappApiSlice = updatedApiSlice.injectEndpoints({
  endpoints: builder => ({
    getWhatsappProfiles: builder.query<
      ApiResponse<PaginatedResponse<WhatsappProfile>>,
      PaginationParams
    >({
      query: (_params = { page: 1, limit: 20 }) => {
        return {
          url: `/wa/profiles`,
          method: 'GET',
        };
      },

      providesTags: ['WhatsappProfile'],
    }),

    getWhatsappProfileById: builder.query<WhatsappProfile, string>({
      query: phoneNumberId => ({
        url: `/wa/profile/${phoneNumberId}`,
        method: 'GET',
      }),
      transformResponse: (response: { data: WhatsappProfile }) => response.data,
      providesTags: ['WhatsappProfile'],
    }),

    updateWhatsappProfile: builder.mutation<
      ApiResponse<WhatsappProfile>,
      { phoneNumberId: string; payload: CreateWhatsappProfileRequest }
    >({
      query: ({ phoneNumberId, payload }) => {
        return {
          url: `/wa/profile/${phoneNumberId}`,
          method: 'POST',
          body: payload,
        };
      },
      invalidatesTags: ['WhatsappProfile'],
    }),

    connectWABANumberToBot: builder.mutation<
      ApiResponse<WhatsappProfile>,
      ConnectWABANumberToBotRequest
    >({
      query: payload => ({
        url: `/wa/connect-waba-bot`,
        method: 'POST',
        body: payload,
      }),
      invalidatesTags: ['WhatsappProfile'],
    }),
  }),
});

export const {
  useGetWhatsappProfilesQuery,
  useGetWhatsappProfileByIdQuery,
  useUpdateWhatsappProfileMutation,
  useConnectWABANumberToBotMutation,
} = whatsappApiSlice;
