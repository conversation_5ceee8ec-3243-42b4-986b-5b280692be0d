import { RootState } from './store';

export enum ApiSliceIdentifier {
  BOT_BUILDER_SERVICE,
  BOT_INTERACTION_SERVICE,
  TEMP_BOT_BUILDER_SERVICE,
  TEMP_BOT_INTERACTION_SERVICE,
  CHATBOT_SERVICE,
}

export function getBaseUrl(apiSliceIdentifier?: ApiSliceIdentifier) {
  //TODO: need to handle it through env
  const baseUrlMap: Partial<Record<ApiSliceIdentifier, string | undefined>> = {
    [ApiSliceIdentifier.BOT_BUILDER_SERVICE]: 'https://chatbot.ngagecpaas.com/api/v1',
    [ApiSliceIdentifier.BOT_INTERACTION_SERVICE]: 'https://chatbot.ngagecpaas.com/api/v1',
    [ApiSliceIdentifier.CHATBOT_SERVICE]: 'https://cms.chatbot.ngagecpaas.com',
    [ApiSliceIdentifier.TEMP_BOT_BUILDER_SERVICE]:
      'https://bot-builder-ingress-1479309560.ap-south-1.elb.amazonaws.com/api/v1',
    [ApiSliceIdentifier.TEMP_BOT_INTERACTION_SERVICE]:
      'https://bot-interaction-ingress-629847414.ap-south-1.elb.amazonaws.com/api/v1',
  };
  if (!Number.isInteger(apiSliceIdentifier))
    return baseUrlMap[ApiSliceIdentifier.BOT_BUILDER_SERVICE];

  return baseUrlMap[apiSliceIdentifier!];
}
