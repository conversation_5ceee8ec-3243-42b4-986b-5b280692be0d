export const dia = {
  ToolsView: class {
    constructor(opts?: any) {
      return opts || {};
    }
  },
  Paper: class {
    on() {}
    remove() {}
  },
  Graph: class {},
  Element: {
    extend: (config: any) => class {},
  },
  Link: class {},
};

export const linkTools = {
  Remove: class {
    constructor(opts?: any) {
      return {
        ...opts,
        ...{
          options: {
            markup: [
              {
                tagName: 'circle',
                selector: 'button',
                attributes: {
                  r: 7,
                  fill: '#FF0000',
                  cursor: 'pointer',
                },
              },
              {
                tagName: 'path',
                selector: 'icon',
                attributes: {
                  d: 'M -3 -3 3 3 M -3 3 3 -3',
                  fill: 'none',
                  stroke: '#FFFFFF',
                  'stroke-width': 2,
                  'pointer-events': 'none',
                },
              },
            ],
          },
        },
      };
    }
  },
};

export const elementTools = {
  Remove: class {
    constructor(opts?: any) {
      return opts || {};
    }
  },
  Button: class {
    static extend(config: any) {
      return class {};
    }
  },
};

export const shapes = {
  standard: {
    Rectangle: class {},
    Circle: class {},
  },
};
