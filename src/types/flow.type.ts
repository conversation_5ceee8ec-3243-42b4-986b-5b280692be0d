export enum FlowType {
  DEFAULT = 'Default',
  CUSTOM = 'Custom',
}

export interface FlowNode {
  id: string;
  botId: string;
  name: string;
  description?: string;
  status?: string;
  type: FlowType;
  appId: string;
  metadata?: Record<string, any>;
  createdAt?: Date;
  updatedAt?: Date;
  deletedAt?: Date;
  createdBy: string;
  updatedBy: string;
  deletedBy?: string;
}

export interface CreateSingleFlowPayload {
  name?: string;
  description?: string;
  botId: string;
  entryNodeId?: string;
  nodes?: Record<string, any>;
  metadata?: Record<string, any>;
  type?: FlowType;
}
