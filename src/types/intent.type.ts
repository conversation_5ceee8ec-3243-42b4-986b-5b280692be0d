import { PaginationParams } from './pagination.type';
import { Language } from './language.type';

// Intent Item types

export enum IntentType {
  DEFAULT = 'DEFAULT',
  CUSTOM = 'CUSTOM',
}
export interface IntentItem {
  id: string;
  botId: string;
  flowId?: string;
  name: string;
  description?: string;
  type: IntentType;
  createdAt: string;
  updatedAt: string;
  deletedAt?: string;
  createdBy: string;
  updatedBy: string;
  deletedBy?: string;
}

export interface CreateIntentItemRequest {
  botId: string;
  flowId?: string;
  name: string;
  description?: string;
}

export interface UpdateIntentItemRequest {
  botId?: string;
  flowId?: string;
  name?: string;
  description?: string;
}

export interface AssignFlowToIntentRequest {
  flowId: string;
  intentId: string;
}

// Intent Utterance types - included in IntentUtteranceTranslation response
export interface IntentUtterance {
  id: string;
  intentId: string;
  metadata?: Record<string, any>;
  createdAt: string;
  createdBy: string;
}

// Intent Utterance Translation types
export interface IntentUtteranceTranslation {
  id: string;
  utteranceId: string;
  langId: string;
  text: string;
  entities?: Record<string, any>;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  updatedBy: string;
  availableLanguages?: Omit<Language, 'createdAt'>[];
}

export interface IntentUtteranceParams {
  intentId: string;
  langId: string;
}
export type IntentUtteranceGetAllParams = IntentUtteranceParams & { query?: PaginationParams };

export interface CreateUtteranceTranslationRequest {
  utteranceId?: string;
  intentId: string;
  langId: string;
  text: string;
  entities?: Record<string, any>;
}

export interface UpdateUtteranceTranslationRequest {
  text?: string;
  entities?: Record<string, any>;
}

export interface UtteranceIdParam {
  utteranceId: string;
}

export interface UtteranceTranslationByLangParam {
  utteranceId: string;
  langId: string;
}
