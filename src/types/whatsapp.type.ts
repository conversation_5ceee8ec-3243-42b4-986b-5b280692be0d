import { OnboardingOptionId } from '../modules/Channels/Whatsapp/config';

export interface WhatsappProfile {
  id: string;
  phone_number_id: string;
  wa_number: string;
  name: string;
  webhookUrl?: string;
  profilePictureUrl?: string;
  about?: string;
  description?: string;
  address?: string;
  email?: string;
  websites?: string[];
  industry?: string;
}

export interface CreateWhatsappProfileRequest {
  name: string;
  profilePictureUrl?: string;
  about?: string;
  description?: string;
  address?: string;
  email?: string;
  websites?: string[];
  industry?: string;
}

export interface ConnectWABANumberToBotRequest {
  botId: string;
  channelId: string;
  wabaPhoneNumberId: string;
  wabaNumber: string;
  onboardingPlatform: OnboardingOptionId | null;
}
